<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>患者诊断详情 - 医学影像诊断系统</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: 'Arial', sans-serif;
        }
        
        body {
            display: flex;
            min-height: 100vh;
            flex-direction: column;
            background-color: #f6f9fc;
        }
        
        .navbar {
            display: flex;
            justify-content: space-between;
            align-items: center;
            height: 60px;
            background-color: white;
            border-bottom: 1px solid #edf2f9;
        }
        
        .left-side {
            display: flex;
            align-items: center;
            padding-left: 20px;
        }
        
        .center-side {
            flex: 1;
        }
        
        .right-side {
            display: flex;
            padding-right: 20px;
            list-style: none;
            align-items: center;
        }
        
        .right-side li {
            display: flex;
            align-items: center;
            padding: 0 10px;
        }
        
        .nav-btn {
            display: flex;
            align-items: center;
            justify-content: center;
            width: 32px;
            height: 32px;
            border-radius: 50%;
            border: 1px solid #e9ecef;
            background-color: transparent;
            color: #6c757d;
            cursor: pointer;
            transition: all 0.2s;
        }
        
        .nav-btn:hover {
            background-color: #f8f9fa;
        }
        
        .badge {
            position: relative;
        }
        
        .badge::after {
            content: '';
            position: absolute;
            top: -2px;
            right: -2px;
            width: 8px;
            height: 8px;
            background-color: #f53f3f;
            border-radius: 50%;
        }
        
        .navbar-title {
            font-weight: bold;
            font-size: 18px;
            color: #444;
            margin: 0;
            margin-left: 10px;
        }
        
        .avatar {
            width: 32px;
            height: 32px;
            border-radius: 50%;
            background-color: #f0f0f0;
            overflow: hidden;
            cursor: pointer;
        }
        
        .main-wrapper {
            display: flex;
            flex: 1;
        }
        
        .main-content {
            flex: 1;
            padding: 20px;

            max-width: 80%;
            margin: 0 auto;
        }
        
        .header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
        }
        
        .header-title {
            font-size: 20px;
            font-weight: bold;
            color: #444;
        }
        
        .header-actions {
            display: flex;
            gap: 10px;
        }
        
        .btn {
            padding: 6px 12px;
            border-radius: 6px;
            font-size: 12px;
            cursor: pointer;
            border: none;
            transition: all 0.2s;
        }
        
        .btn-primary {
            background-color: #6e4ff6;
            color: white;
        }

        .btn-clear {
            background-color: rgb(165, 164, 164);
            color: white;
        }
        
        .btn-secondary {
            background-color: #e9ecef;
            color: #444;
        }
        
        .btn-danger {
            background-color: #ea4335;
            color: white;
        }
        
        .btn:hover {
            opacity: 0.9;
        }
        
        .card {
            background-color: white;
            border-radius: 10px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
            padding: 12px;
            margin-bottom: 20px;
        }
        
        .card-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
            padding-bottom: 10px;
            border-bottom: 1px solid #e9ecef;
        }
        
        .card-title {
            font-size: 18px;
            font-weight: bold;
            color: #444;
        }
        
        .form-group {
            margin-bottom: 15px;
        }
        
        .form-row {
            display: flex;
            gap: 15px;
            margin-bottom: 15px;
        }
        
        .form-control {
            width: 100%;
            padding: 10px;
            border: 1px solid #d0d7de;
            border-radius: 6px;
            font-size: 14px;
        }
        
        .form-label {
            display: block;
            margin-bottom: 5px;
            font-size: 14px;
            color: #444;
        }
        
        /* Image viewer specific styles */
        .image-viewer {
            display: flex;
            flex-direction: column;
            height: 100%;
        }
        
        .image-tabs {
            display: flex;
            border-bottom: 1px solid #e9ecef;
            margin-bottom: 15px;
        }
        
        .image-tab {
            padding: 10px 15px;
            cursor: pointer;
            border-bottom: 2px solid transparent;
            font-size: 14px;
            color: #444;
        }
        
        .image-tab.active {
            border-bottom-color: #6e4ff6;
            color: #6e4ff6;
            font-weight: 500;
        }
        
        .image-container {
            display: flex;
            flex-wrap: wrap;
            gap: 20px;
            justify-content: center;
        }
        
        .image-display {
            flex: 1;
            display: flex;
            flex-direction: column;
            align-items: center;
        }
        
        .image-wrapper {
            position: relative;
            margin-bottom: 10px;
        }
        
        .mri-image {
            max-width: 100%;
            border-radius: 5px;
        }
        
        .image-controls {
            display: flex;
            justify-content: center;
            gap: 10px;
            margin-top: 10px;
        }
        
        .contrast-slider {
            width: 100%;
            max-width: 230px;
        }
        
        .contrast-control {
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .contrast-control .form-label {
            margin-bottom: 0;
            white-space: nowrap;
            min-width: 50px;
        }
        
        .textarea-control {
            width: 100%;
            padding: 10px;
            border: 1px solid #d0d7de;
            border-radius: 6px;
            min-height: 150px;
            font-size: 14px;
            resize: vertical;
        }
        
        .card-footer {
            display: flex;
            justify-content: flex-end;
            gap: 10px;
            margin-top: 20px;
            padding-top: 15px;
            border-top: 1px solid #e9ecef;
        }
        
        .info-item {
            display: flex;
            margin-bottom: 12px;
            align-items: center;
        }
        
        .info-label {
            width: 80px;
            font-size: 14px;
            color: #6c757d;
            flex-shrink: 0;
        }
        
        .info-value {
            font-size: 14px;
            color: #333;
            font-weight: 500;
            margin-right: 30px;
        }
        
        .info-grid {
            display: flex;
            flex-wrap: wrap;
            padding: 5px 0;
        }

        .info-row {
            display: flex;
            width: 100%;
            margin-bottom: 15px;
        }

        /* Personal info table styles */
        .info-table {
            width: 100%;
            border-collapse: collapse;
            border: 1px solid #edf2f9;
            background-color: #fff;
            margin-top: 10px;
        }
        
        .info-table td {
            padding: 12px 15px;
            border: 1px solid #edf2f9;
            font-size: 14px;
        }
        
        .info-table-label {
            background-color: #f9fafb;
            color: #6c757d;
            font-weight: normal;
            width: 100px;
            text-align: right;
        }
        
        .info-table-value {
            color: #333;
        }
    </style>
</head>
<body>
    <div class="navbar">
        <div class="left-side">
            <img alt="logo" src="https://via.placeholder.com/32" style="width: 32px; height: 32px" />
            <h5 class="navbar-title">医学影像诊断系统</h5>
        </div>
        <div class="center-side">
            <!-- Reserved for top menu if needed -->
        </div>
        <ul class="right-side">
            <li>
                <button class="nav-btn" title="搜索">
                    <svg xmlns="http://www.w3.org/2000/svg" width="12" height="12" fill="currentColor" viewBox="0 0 16 16">
                        <path d="M11.742 10.344a6.5 6.5 0 1 0-1.397 1.398h-.001c.03.04.062.078.098.115l3.85 3.85a1 1 0 0 0 1.415-1.414l-3.85-3.85a1.007 1.007 0 0 0-.115-.1zM12 6.5a5.5 5.5 0 1 1-11 0 5.5 5.5 0 0 1 11 0z"/>
                    </svg>
                </button>
            </li>
            <li>
                <div class="badge">
                    <button class="nav-btn" title="通知">
                        <svg xmlns="http://www.w3.org/2000/svg" width="12" height="12" fill="currentColor" viewBox="0 0 16 16">
                            <path d="M8 16a2 2 0 0 0 2-2H6a2 2 0 0 0 2 2zm.995-14.901a1 1 0 1 0-1.99 0A5.002 5.002 0 0 0 3 6c0 1.098-.5 6-2 7h14c-1.5-1-2-5.902-2-7 0-2.42-1.72-4.44-4.005-4.901z"/>
                        </svg>
                    </button>
                </div>
            </li>
            <li>
                <div class="avatar">
                    <img src="https://via.placeholder.com/32" alt="User Avatar">
                </div>
            </li>
        </ul>
    </div>
    
    <div class="main-wrapper">
        <div class="main-content">
            <div class="header">
                <div class="header-title">患者诊断详情</div>
                <div class="header-actions">
                    <button class="btn btn-secondary">返回列表</button>
                </div>
            </div>
            
            <!-- 个人信息卡片 -->
            <div class="card">
                <div class="card-header">
                    <div class="card-title">个人信息</div>
                </div>
                <table class="info-table">
                    <tr>
                        <td class="info-table-label">姓名</td>
                        <td class="info-table-value">王小明</td>
                        <td class="info-table-label">检查日期</td>
                        <td class="info-table-value">2023-07-10</td>
                        <td class="info-table-label">性别</td>
                        <td class="info-table-value">男</td>
                    </tr>
                    <tr>
                        <td class="info-table-label">年龄</td>
                        <td class="info-table-value">45</td>
                        <td class="info-table-label">备注</td>
                        <td class="info-table-value" colspan="3">头痛头晕，需进一步检查</td>
                    </tr>
                </table>
            </div>
            
            <!-- 图像查看器卡片 -->
            <div class="card" style="height: 500px;">
                <!-- <div class="card-header">
                    <div class="card-title">影像查看</div>
                </div> -->
                <div class="image-viewer">
                    <div class="image-tabs">
                        <div class="image-tab active">T2 图像</div>
                        <div class="image-tab">脑血管图像</div>
                        <div class="image-tab">T1 图像</div>
                    </div>
                    <div class="image-container">
                        <div class="image-display">
                            <div class="image-wrapper">
                                <img src="https://via.placeholder.com/450x350" alt="MRI Image" class="mri-image">
                            </div>
                            <div class="image-controls">
                                <button class="btn btn-secondary">
                                    <svg xmlns="http://www.w3.org/2000/svg" width="11" height="11" fill="currentColor" viewBox="0 0 16 16">
                                        <path d="M8 15A7 7 0 1 1 8 1a7 7 0 0 1 0 14zm0 1A8 8 0 1 0 8 0a8 8 0 0 0 0 16z"/>
                                        <path d="M8 4a.5.5 0 0 1 .5.5v3h3a.5.5 0 0 1 0 1h-3v3a.5.5 0 0 1-1 0v-3h-3a.5.5 0 0 1 0-1h3v-3A.5.5 0 0 1 8 4z"/>
                                    </svg>
                                    水平翻转
                                </button>
                                <button class="btn btn-secondary">
                                    <svg xmlns="http://www.w3.org/2000/svg" width="11" height="11" fill="currentColor" viewBox="0 0 16 16">
                                        <path d="M8 15A7 7 0 1 1 8 1a7 7 0 0 1 0 14zm0 1A8 8 0 1 0 8 0a8 8 0 0 0 0 16z"/>
                                        <path d="M8 4a.5.5 0 0 1 .5.5v3h3a.5.5 0 0 1 0 1h-3v3a.5.5 0 0 1-1 0v-3h-3a.5.5 0 0 1 0-1h3v-3A.5.5 0 0 1 8 4z"/>
                                    </svg>
                                    垂直翻转
                                </button>
                                <button class="btn btn-secondary">
                                    <svg xmlns="http://www.w3.org/2000/svg" width="11" height="11" fill="currentColor" viewBox="0 0 16 16">
                                        <path d="M8 15A7 7 0 1 1 8 1a7 7 0 0 1 0 14zm0 1A8 8 0 1 0 8 0a8 8 0 0 0 0 16z"/>
                                        <path d="M8 4a.5.5 0 0 1 .5.5v3h3a.5.5 0 0 1 0 1h-3v3a.5.5 0 0 1-1 0v-3h-3a.5.5 0 0 1 0-1h3v-3A.5.5 0 0 1 8 4z"/>
                                    </svg>
                                    90°旋转
                                </button>
                            </div>
                            <div class="form-group contrast-control" style="width: 100%; max-width: 450px;">
                                <label class="form-label">对比度</label>
                                <input type="range" class="contrast-slider form-control" min="0" max="100" value="50">
                            </div>
                        </div>
                        
                        <div class="image-display">
                            <div class="image-wrapper">
                                <img src="https://via.placeholder.com/450x350" alt="MRI Image" class="mri-image">
                            </div>
                            <div class="image-controls">
                                <button class="btn btn-secondary">
                                    <svg xmlns="http://www.w3.org/2000/svg" width="11" height="11" fill="currentColor" viewBox="0 0 16 16">
                                        <path d="M8 15A7 7 0 1 1 8 1a7 7 0 0 1 0 14zm0 1A8 8 0 1 0 8 0a8 8 0 0 0 0 16z"/>
                                        <path d="M8 4a.5.5 0 0 1 .5.5v3h3a.5.5 0 0 1 0 1h-3v3a.5.5 0 0 1-1 0v-3h-3a.5.5 0 0 1 0-1h3v-3A.5.5 0 0 1 8 4z"/>
                                    </svg>
                                    水平翻转
                                </button>
                                <button class="btn btn-secondary">
                                    <svg xmlns="http://www.w3.org/2000/svg" width="11" height="11" fill="currentColor" viewBox="0 0 16 16">
                                        <path d="M8 15A7 7 0 1 1 8 1a7 7 0 0 1 0 14zm0 1A8 8 0 1 0 8 0a8 8 0 0 0 0 16z"/>
                                        <path d="M8 4a.5.5 0 0 1 .5.5v3h3a.5.5 0 0 1 0 1h-3v3a.5.5 0 0 1-1 0v-3h-3a.5.5 0 0 1 0-1h3v-3A.5.5 0 0 1 8 4z"/>
                                    </svg>
                                    垂直翻转
                                </button>
                                <button class="btn btn-secondary">
                                    <svg xmlns="http://www.w3.org/2000/svg" width="11" height="11" fill="currentColor" viewBox="0 0 16 16">
                                        <path d="M8 15A7 7 0 1 1 8 1a7 7 0 0 1 0 14zm0 1A8 8 0 1 0 8 0a8 8 0 0 0 0 16z"/>
                                        <path d="M8 4a.5.5 0 0 1 .5.5v3h3a.5.5 0 0 1 0 1h-3v3a.5.5 0 0 1-1 0v-3h-3a.5.5 0 0 1 0-1h3v-3A.5.5 0 0 1 8 4z"/>
                                    </svg>
                                    90°旋转
                                </button>
                            </div>
                            <div class="form-group contrast-control" style="width: 100%; max-width: 450px;">
                                <label class="form-label">对比度</label>
                                <input type="range" class="contrast-slider form-control" min="0" max="100" value="50">
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 诊断结果卡片 -->
            <div class="card">
                <div class="card-header">
                    <div class="card-title">诊断结果</div>
                </div>
                <div class="form-group">
                    <textarea class="textarea-control" placeholder="请输入诊断结果..."></textarea>
                </div>
                <div class="card-footer">
                    <button class="btn btn-clear">清空</button>
                    <button class="btn btn-primary">保存</button>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 这里可以放置交互逻辑，如图像翻转、旋转等功能
        document.addEventListener('DOMContentLoaded', function() {
            // 切换图像选项卡
            const tabs = document.querySelectorAll('.image-tab');
            tabs.forEach(tab => {
                tab.addEventListener('click', function() {
                    tabs.forEach(t => t.classList.remove('active'));
                    this.classList.add('active');
                });
            });
            
            // 可以添加更多交互逻辑
        });
    </script>
</body>
</html> 