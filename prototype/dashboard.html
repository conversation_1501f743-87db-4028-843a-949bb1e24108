<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>实验室仪器设备管理系统</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: 'Arial', sans-serif;
        }
        
        body {
            display: flex;
            min-height: 100vh;
            flex-direction: column;
        }
        
        .navbar {
            display: flex;
            justify-content: space-between;
            align-items: center;
            height: 60px;
            background-color: white;
            border-bottom: 1px solid #edf2f9;
        }
        
        .left-side {
            display: flex;
            align-items: center;
            padding-left: 20px;
        }
        
        .center-side {
            flex: 1;
        }
        
        .right-side {
            display: flex;
            padding-right: 20px;
            list-style: none;
            align-items: center;
        }
        
        .right-side li {
            display: flex;
            align-items: center;
            padding: 0 10px;
        }
        
        .nav-btn {
            display: flex;
            align-items: center;
            justify-content: center;
            width: 32px;
            height: 32px;
            border-radius: 50%;
            border: 1px solid #e9ecef;
            background-color: transparent;
            color: #6c757d;
            cursor: pointer;
            transition: all 0.2s;
        }
        
        .nav-btn:hover {
            background-color: #f8f9fa;
        }
        
        .badge {
            position: relative;
        }
        
        .badge::after {
            content: '';
            position: absolute;
            top: -2px;
            right: -2px;
            width: 8px;
            height: 8px;
            background-color: #f53f3f;
            border-radius: 50%;
        }
        
        .navbar-title {
            font-weight: bold;
            font-size: 18px;
            color: #444;
            margin: 0;
            margin-left: 10px;
        }
        
        .avatar {
            width: 32px;
            height: 32px;
            border-radius: 50%;
            background-color: #f0f0f0;
            overflow: hidden;
            cursor: pointer;
        }
        
        .main-wrapper {
            display: flex;
            flex: 1;
        }
        
        .sidebar {
            width: 150px;
            background-color: white;
            padding: 20px 0;
            color: #444;
            border-right: 1px solid #edf2f9;
        }
        
        .logo {
            display: flex;
            align-items: center;
            padding: 15px 20px;
            margin-bottom: 25px;
        }
        
        .logo-icon {
            width: 35px;
            height: 35px;
            background-color: #6e4ff6;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            margin-right: 10px;
            box-shadow: 0 3px 6px rgba(110, 79, 246, 0.2);
        }
        
        .logo-text {
            font-weight: bold;
            font-size: 18px;
            color: #444;
        }
        
        .logo-badge {
            font-size: 12px;
            color: #6c757d;
            margin-left: 5px;
        }
        
        .menu-item {
            display: flex;
            align-items: center;
            padding: 12px 15px;
            cursor: pointer;
            transition: all 0.2s ease;
            color: #6c757d;
            margin-bottom: 5px;
            border-radius: 5px;
            margin-left: 10px;
            margin-right: 10px;
            position: relative;
        }
        
        .menu-item:hover {
            background-color: rgba(110, 79, 246, 0.05);
            color: #6e4ff6;
        }
        
        .menu-item.active {
            background-color: #6e4ff6;
            color: white;
            font-weight: 500;
            box-shadow: 0 4px 10px rgba(110, 79, 246, 0.25);
        }
        
        .menu-icon {
            width: 20px;
            height: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 10px;
            opacity: 0.7;
        }
        
        .menu-item.active .menu-icon {
            opacity: 1;
        }
        
        .menu-badge {
            position: absolute;
            top: 10px;
            right: 15px;
            background-color: #ff4757;
            color: white;
            font-size: 9px;
            font-weight: 300;
            height: 18px;
            min-width: 18px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 20px;
            padding: 0 3px;
        }
        
        .user-profile {
            display: flex;
            align-items: center;
            padding: 15px 20px;
            border-top: 1px solid #edf2f9;
            margin-top: auto;
            color: #444;
        }
        
        .main-content {
            flex: 1;
            padding: 20px;
            background-color: #f6f9fc;
        }
        
        .header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 30px;
        }
        
        .greeting {
            font-size: 18px;
            font-weight: bold;
            color: #444;
        }
        
        .stats {
            display: flex;
            flex-wrap: wrap;
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .stat-card {
            flex: 1;
            min-width: 180px;
            padding: 12px;
            border-radius: 15px;
            background-color: white;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
            position: relative;
            border: none;
        }
        
        .stat-icon {
            position: absolute;
            top: 20px;
            right: 20px;
            width: 40px;
            height: 40px;
            border-radius: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            opacity: 0.9;
        }
        
        .stat-label {
            font-size: 14px;
            color: #6c757d;
            margin-bottom: 8px;
        }
        
        .stat-value {
            font-size: 25px;
            font-weight: bold;
            color: #333;
            margin-bottom: 3px;
        }
        
        .stat-trend {
            display: flex;
            align-items: center;
            font-size: 13px;
            margin-top: 5px;
        }
        
        .trend-up {
            color: #2ed573;
        }
        
        .trend-down {
            color: #ff4757;
        }
        
        .trend-icon {
            margin-right: 5px;
        }
        
        .stat-icon.head {
            background-color: #e7dbff;
            color: #6e4ff6;
        }
        
        .stat-icon.abdomen {
            background-color: #fff5d8;
            color: #ffb74a;
        }
        
        .stat-icon.cervical {
            background-color: #dffff0;
            color: #4adf8f;
        }
        
        .stat-icon.lumbar {
            background-color: #e0e0ff;
            color: #7a77ff;
        }
        
        .stat-icon.other {
            background-color: #f9e0ff;
            color: #c27afa;
        }
        
        .table-container {
            background-color: white;
            border-radius: 10px;
            padding: 20px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
        }
        
        .table-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
        }
        
        .table-title {
            font-size: 18px;
            font-weight: bold;
            color: #444;
        }
        
        .table-subtitle {
            font-size: 14px;
            color: #6c757d;
            margin-top: 5px;
        }
        
        .table-actions {
            display: flex;
            gap: 10px;
        }
        
        table {
            width: 100%;
            border-collapse: collapse;
        }
        
        th, td {
            padding: 15px;
            text-align: left;
            border-bottom: 1px solid #e9ecef;
        }
        
        th {
            color: #6c757d;
            font-weight: normal;
            font-size: 14px;
            background-color: #f8f9fa;
        }
        
        .status {
            padding: 5px 10px;
            border-radius: 20px;
            font-size: 12px;
            display: inline-block;
        }
        
        .active {
            background-color: rgba(52, 168, 83, 0.1);
            color: #34a853;
        }
        
        .inactive {
            background-color: rgba(234, 67, 53, 0.1);
            color: #ea4335;
        }
        
        .btn {
            padding: 5px 10px;
            border-radius: 4px;
            font-size: 12px;
            cursor: pointer;
            margin-right: 5px;
            border: none;
        }
        
        .btn-edit {
            background-color: #6e4ff6;
            color: white;
        }
        
        .btn-pending {
            background-color: #fbbc05;
            color: white;
        }
        
        .pagination {
            display: flex;
            justify-content: center;
            margin-top: 20px;
            gap: 5px;
        }
        
        .page {
            width: 30px;
            height: 30px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 5px;
            cursor: pointer;
        }
        
        .page.active {
            background-color: #6e4ff6;
            color: white;
        }
        
        .page:hover:not(.active) {
            background-color: #e9ecef;
        }
        
        .category-item {
            display: flex;
            align-items: center;
            margin-bottom: 8px;
        }
        
        .category-label {
            width: 45px;
            font-size: 12px;
            color: #6c757d;
        }
        
        .category-count {
            width: 25px;
            font-size: 12px;
            font-weight: bold;
            text-align: right;
            padding-right: 8px;
            color: #444;
        }
        
        .progress-container {
            flex: 1;
            height: 6px;
            background-color: #f0f0f0;
            border-radius: 3px;
            overflow: hidden;
        }
        
        .progress-bar {
            height: 100%;
            border-radius: 3px;
        }
        
        .progress-head {
            background-color: #6e4ff6;
        }
        
        .progress-abdomen {
            background-color: #42ba96;
        }
        
        .progress-cervical {
            background-color: #fbbc05;
        }
        
        .progress-lumbar {
            background-color: #ea4335;
        }
        
        .progress-other {
            background-color: #4285f4;
        }
    </style>
</head>
<body>
    <div class="navbar">
        <div class="left-side">
            <img alt="logo" src="https://via.placeholder.com/32" style="width: 32px; height: 32px" />
            <h5 class="navbar-title">医学影像诊断系统</h5>
        </div>
        <div class="center-side">
            <!-- Reserved for top menu if needed -->
        </div>
        <ul class="right-side">
            <li>
                <button class="nav-btn" title="搜索">
                    <svg xmlns="http://www.w3.org/2000/svg" width="12" height="12" fill="currentColor" viewBox="0 0 16 16">
                        <path d="M11.742 10.344a6.5 6.5 0 1 0-1.397 1.398h-.001c.03.04.062.078.098.115l3.85 3.85a1 1 0 0 0 1.415-1.414l-3.85-3.85a1.007 1.007 0 0 0-.115-.1zM12 6.5a5.5 5.5 0 1 1-11 0 5.5 5.5 0 0 1 11 0z"/>
                    </svg>
                </button>
            </li>
            <li>
                <button class="nav-btn" title="切换主题">
                    <svg xmlns="http://www.w3.org/2000/svg" width="12" height="12" fill="currentColor" viewBox="0 0 16 16">
                        <path d="M6 .278a.768.768 0 0 1 .08.858 7.208 7.208 0 0 0-.878 3.46c0 4.021 3.278 7.277 7.318 7.277.527 0 1.04-.055 1.533-.16a.787.787 0 0 1 .81.316.733.733 0 0 1-.031.893A8.349 8.349 0 0 1 8.344 16C3.734 16 0 12.286 0 7.71 0 4.266 2.114 1.312 5.124.06A.752.752 0 0 1 6 .278z"/>
                    </svg>
                </button>
            </li>
            <li>
                <div class="badge">
                    <button class="nav-btn" title="通知">
                        <svg xmlns="http://www.w3.org/2000/svg" width="12" height="12" fill="currentColor" viewBox="0 0 16 16">
                            <path d="M8 16a2 2 0 0 0 2-2H6a2 2 0 0 0 2 2zm.995-14.901a1 1 0 1 0-1.99 0A5.002 5.002 0 0 0 3 6c0 1.098-.5 6-2 7h14c-1.5-1-2-5.902-2-7 0-2.42-1.72-4.44-4.005-4.901z"/>
                        </svg>
                    </button>
                </div>
            </li>
            <li>
                <button class="nav-btn" title="全屏">
                    <svg xmlns="http://www.w3.org/2000/svg" width="12" height="12" fill="currentColor" viewBox="0 0 16 16">
                        <path d="M1.5 1a.5.5 0 0 0-.5.5v4a.5.5 0 0 1-1 0v-4A1.5 1.5 0 0 1 1.5 0h4a.5.5 0 0 1 0 1h-4zM10 .5a.5.5 0 0 1 .5-.5h4A1.5 1.5 0 0 1 16 1.5v4a.5.5 0 0 1-1 0v-4a.5.5 0 0 0-.5-.5h-4a.5.5 0 0 1-.5-.5zM.5 10a.5.5 0 0 1 .5.5v4a.5.5 0 0 0 .5.5h4a.5.5 0 0 1 0 1h-4A1.5 1.5 0 0 1 0 14.5v-4a.5.5 0 0 1 .5-.5zm15 0a.5.5 0 0 1 .5.5v4a1.5 1.5 0 0 1-1.5 1.5h-4a.5.5 0 0 1 0-1h4a.5.5 0 0 0 .5-.5v-4a.5.5 0 0 1 .5-.5z"/>
                    </svg>
                </button>
            </li>
            <li>
                <div class="avatar">
                    <img src="https://via.placeholder.com/32" alt="User Avatar">
                </div>
            </li>
        </ul>
    </div>
    
    <div class="main-wrapper">
        <div class="sidebar">
            <div class="menu-item active">
                <div class="menu-icon">
                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" viewBox="0 0 16 16">
                        <path d="M8 8a3 3 0 1 0 0-6 3 3 0 0 0 0 6zm0 2a6 6 0 0 0-6 6h12a6 6 0 0 0-6-6z"/>
                    </svg>
                </div>
                头颅
                <div class="menu-badge">78</div>
            </div>
            
            <div class="menu-item">
                <div class="menu-icon">
                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" viewBox="0 0 16 16">
                        <path d="M12 1a1 1 0 0 1 1 1v12a1 1 0 0 1-1 1H4a1 1 0 0 1-1-1V2a1 1 0 0 1 1-1h8zM4 0a2 2 0 0 0-2 2v12a2 2 0 0 0 2 2h8a2 2 0 0 0 2-2V2a2 2 0 0 0-2-2H4z"/>
                        <path d="M4 2.5a.5.5 0 0 1 .5-.5h7a.5.5 0 0 1 .5.5v2a.5.5 0 0 1-.5.5h-7a.5.5 0 0 1-.5-.5v-2zm0 4a.5.5 0 0 1 .5-.5h1a.5.5 0 0 1 .5.5v1a.5.5 0 0 1-.5.5h-1a.5.5 0 0 1-.5-.5v-1zm0 3a.5.5 0 0 1 .5-.5h1a.5.5 0 0 1 .5.5v1a.5.5 0 0 1-.5.5h-1a.5.5 0 0 1-.5-.5v-1z"/>
                    </svg>
                </div>
                腹部
                <div class="menu-badge">45</div>
            </div>
            
            <div class="menu-item">
                <div class="menu-icon">
                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" viewBox="0 0 16 16">
                        <path d="M6 2a.5.5 0 0 1 .47.33L10 12.036l1.53-4.208A.5.5 0 0 1 12 7.5h3.5a.5.5 0 0 1 0 1h-3.15l-1.88 5.17a.5.5 0 0 1-.94 0L6 3.964 4.47 8.171A.5.5 0 0 1 4 8.5H.5a.5.5 0 0 1 0-1h3.15l1.88-5.17A.5.5 0 0 1 6 2Z"/>
                    </svg>
                </div>
                颈椎
                <div class="menu-badge">32</div>
            </div>
            
            <div class="menu-item">
                <div class="menu-icon">
                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" viewBox="0 0 16 16">
                        <path d="M5 11.5a.5.5 0 0 1 .5-.5h9a.5.5 0 0 1 0 1h-9a.5.5 0 0 1-.5-.5zm0-4a.5.5 0 0 1 .5-.5h9a.5.5 0 0 1 0 1h-9a.5.5 0 0 1-.5-.5zm0-4a.5.5 0 0 1 .5-.5h9a.5.5 0 0 1 0 1h-9a.5.5 0 0 1-.5-.5z"/>
                        <path d="M1.713 11.865v-.474H2c.217 0 .363-.137.363-.317 0-.185-.158-.31-.361-.31-.223 0-.367.152-.373.31h-.59c.016-.467.373-.787.986-.787.588-.002.954.291.957.703a.595.595 0 0 1-.492.594v.033a.615.615 0 0 1 .569.631c.003.533-.502.8-1.051.8-.656 0-1-.37-1.008-.794h.582c.008.178.186.306.422.309.254 0 .424-.145.422-.35-.002-.195-.155-.348-.414-.348h-.3z"/>
                    </svg>
                </div>
                腰椎
                <div class="menu-badge">24</div>
            </div>
            
            <div class="menu-item">
                <div class="menu-icon">
                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" viewBox="0 0 16 16">
                        <path d="M8 9.5a2 2 0 1 0 0-4 2 2 0 0 0 0 4z"/>
                        <path d="M9.5 2.686l.5.5v7.128l-.5.5h-3l-.5-.5V3.186l.5-.5h3zm0-1h-3A1.5 1.5 0 0 0 5 3.186v7.128A1.5 1.5 0 0 0 6.5 12h3A1.5 1.5 0 0 0 11 10.314V3.186A1.5 1.5 0 0 0 9.5 1.686z"/>
                    </svg>
                </div>
                其他
                <div class="menu-badge">10</div>
            </div>
            

        </div>
        
        <div class="main-content">
            <div class="header">
                <div class="greeting">你好，张医生 👋</div>
            </div>
            
            <div class="stats">
                <div class="stat-card head">
                    <div class="stat-icon head">
                        <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" fill="currentColor" viewBox="0 0 16 16">
                            <path d="M8 8a3 3 0 1 0 0-6 3 3 0 0 0 0 6zm0 2a6 6 0 0 0-6 6h12a6 6 0 0 0-6-6z"/>
                        </svg>
                    </div>
                    <div class="stat-info">
                        <div class="stat-value">78</div>
                        <div class="stat-label">头颅</div>
                    </div>
                </div>
                
                <div class="stat-card abdomen">
                    <div class="stat-icon abdomen">
                        <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" fill="currentColor" viewBox="0 0 16 16">
                            <path d="M12 1a1 1 0 0 1 1 1v12a1 1 0 0 1-1 1H4a1 1 0 0 1-1-1V2a1 1 0 0 1 1-1h8zM4 0a2 2 0 0 0-2 2v12a2 2 0 0 0 2 2h8a2 2 0 0 0 2-2V2a2 2 0 0 0-2-2H4z"/>
                            <path d="M4 2.5a.5.5 0 0 1 .5-.5h7a.5.5 0 0 1 .5.5v2a.5.5 0 0 1-.5.5h-7a.5.5 0 0 1-.5-.5v-2z"/>
                        </svg>
                    </div>
                    <div class="stat-info">
                        <div class="stat-value">45</div>
                        <div class="stat-label">腹部</div>
                    </div>
                </div>
                
                <div class="stat-card cervical">
                    <div class="stat-icon cervical">
                        <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" fill="currentColor" viewBox="0 0 16 16">
                            <path d="M6 2a.5.5 0 0 1 .47.33L10 12.036l1.53-4.208A.5.5 0 0 1 12 7.5h3.5a.5.5 0 0 1 0 1h-3.15l-1.88 5.17a.5.5 0 0 1-.94 0L6 3.964 4.47 8.171A.5.5 0 0 1 4 8.5H.5a.5.5 0 0 1 0-1h3.15l1.88-5.17A.5.5 0 0 1 6 2Z"/>
                        </svg>
                    </div>
                    <div class="stat-info">
                        <div class="stat-value">32</div>
                        <div class="stat-label">颈椎</div>
                    </div>
                </div>
                
                <div class="stat-card lumbar">
                    <div class="stat-icon lumbar">
                        <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" fill="currentColor" viewBox="0 0 16 16">
                            <path d="M5 11.5a.5.5 0 0 1 .5-.5h9a.5.5 0 0 1 0 1h-9a.5.5 0 0 1-.5-.5zm0-4a.5.5 0 0 1 .5-.5h9a.5.5 0 0 1 0 1h-9a.5.5 0 0 1-.5-.5zm0-4a.5.5 0 0 1 .5-.5h9a.5.5 0 0 1 0 1h-9a.5.5 0 0 1-.5-.5z"/>
                            <path d="M1.713 11.865v-.474H2c.217 0 .363-.137.363-.317 0-.185-.158-.31-.361-.31-.223 0-.367.152-.373.31h-.59c.016-.467.373-.787.986-.787.588-.002.954.291.957.703a.595.595 0 0 1-.492.594v.033a.615.615 0 0 1 .569.631c.003.533-.502.8-1.051.8-.656 0-1-.37-1.008-.794h.582c.008.178.186.306.422.309.254 0 .424-.145.422-.35-.002-.195-.155-.348-.414-.348h-.3z"/>
                        </svg>
                    </div>
                    <div class="stat-info">
                        <div class="stat-value">24</div>
                        <div class="stat-label">腰椎</div>
                    </div>
                </div>
                
                <div class="stat-card other">
                    <div class="stat-icon other">
                        <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" fill="currentColor" viewBox="0 0 16 16">
                            <path d="M8 9.5a2 2 0 1 0 0-4 2 2 0 0 0 0 4z"/>
                            <path d="M9.5 2.686l.5.5v7.128l-.5.5h-3l-.5-.5V3.186l.5-.5h3zm0-1h-3A1.5 1.5 0 0 0 5 3.186v7.128A1.5 1.5 0 0 0 6.5 12h3A1.5 1.5 0 0 0 11 10.314V3.186A1.5 1.5 0 0 0 9.5 1.686z"/>
                        </svg>
                    </div>
                    <div class="stat-info">
                        <div class="stat-value">10</div>
                        <div class="stat-label">其他</div>
                    </div>
                </div>
            </div>
            
            <div class="table-container">
                <div class="table-header">
                    <div>
                        <div class="table-title">所有患者</div>
                        <div class="table-subtitle">活跃检查患者</div>
                    </div>
                    <div class="table-actions">
                        <div style="display: flex; align-items: center; gap: 5px;">
                            <span style="font-size: 14px;">排序:</span>
                            <select style="padding: 5px; border-radius: 5px; border: 1px solid #d0d7de;">
                                <option>最新</option>
                                <option>姓名</option>
                                <option>状态</option>
                            </select>
                        </div>
                    </div>
                </div>
                
                <table>
                    <thead>
                        <tr>
                            <th>序号</th>
                            <th>日期</th>
                            <th>姓名</th>
                            <th>备注</th>
                            <th>诊断老师</th>
                            <th>数据状态</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>1</td>
                            <td>2023-07-10</td>
                            <td>王小明</td>
                            <td>头痛头晕，需进一步检查</td>
                            <td>李教授</td>
                            <td><span class="status active">已完成</span></td>
                            <td>
                                <button class="btn btn-edit">编辑</button>
                                <button class="btn btn-pending">待编辑</button>
                            </td>
                        </tr>
                        <tr>
                            <td>2</td>
                            <td>2023-07-09</td>
                            <td>李红</td>
                            <td>复查</td>
                            <td>张教授</td>
                            <td><span class="status inactive">待处理</span></td>
                            <td>
                                <button class="btn btn-edit">编辑</button>
                                <button class="btn btn-pending">待编辑</button>
                            </td>
                        </tr>
                        <tr>
                            <td>3</td>
                            <td>2023-07-08</td>
                            <td>张伟</td>
                            <td>眩晕症状持续一周</td>
                            <td>王教授</td>
                            <td><span class="status inactive">待处理</span></td>
                            <td>
                                <button class="btn btn-edit">编辑</button>
                                <button class="btn btn-pending">待编辑</button>
                            </td>
                        </tr>
                        <tr>
                            <td>4</td>
                            <td>2023-07-08</td>
                            <td>刘芳</td>
                            <td>定期检查</td>
                            <td>李教授</td>
                            <td><span class="status active">已完成</span></td>
                            <td>
                                <button class="btn btn-edit">编辑</button>
                                <button class="btn btn-pending">待编辑</button>
                            </td>
                        </tr>
                        <tr>
                            <td>5</td>
                            <td>2023-07-07</td>
                            <td>陈明</td>
                            <td>急诊</td>
                            <td>刘教授</td>
                            <td><span class="status active">已完成</span></td>
                            <td>
                                <button class="btn btn-edit">编辑</button>
                                <button class="btn btn-pending">待编辑</button>
                            </td>
                        </tr>
                        <tr>
                            <td>6</td>
                            <td>2023-07-06</td>
                            <td>赵倩</td>
                            <td>外伤后检查</td>
                            <td>陈教授</td>
                            <td><span class="status active">已完成</span></td>
                            <td>
                                <button class="btn btn-edit">编辑</button>
                                <button class="btn btn-pending">待编辑</button>
                            </td>
                        </tr>
                        <tr>
                            <td>7</td>
                            <td>2023-07-06</td>
                            <td>周强</td>
                            <td>头痛伴发热</td>
                            <td>吴教授</td>
                            <td><span class="status active">已完成</span></td>
                            <td>
                                <button class="btn btn-edit">编辑</button>
                                <button class="btn btn-pending">待编辑</button>
                            </td>
                        </tr>
                        <tr>
                            <td>8</td>
                            <td>2023-07-05</td>
                            <td>吴静</td>
                            <td>头部外伤随访</td>
                            <td>赵教授</td>
                            <td><span class="status inactive">待处理</span></td>
                            <td>
                                <button class="btn btn-edit">编辑</button>
                                <button class="btn btn-pending">待编辑</button>
                            </td>
                        </tr>
                    </tbody>
                </table>
                
                <div style="display: flex; justify-content: space-between; align-items: center; margin-top: 20px;">
                    <div style="font-size: 14px; color: #6c757d;">显示数据 1 至 8 共 256 条记录</div>
                    <div class="pagination">
                        <div class="page">&lt;</div>
                        <div class="page active">1</div>
                        <div class="page">2</div>
                        <div class="page">3</div>
                        <div class="page">4</div>
                        <div class="page">...</div>
                        <div class="page">40</div>
                        <div class="page">&gt;</div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>
</html> 