<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>登录 - 医学影像诊断系统</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: 'Arial', sans-serif;
        }
        
        body {
            display: flex;
            min-height: 100vh;
            flex-direction: column;
            background-color: #f6f9fc;
        }
        
        .navbar {
            display: flex;
            justify-content: space-between;
            align-items: center;
            height: 60px;
            background-color: white;
            border-bottom: 1px solid #edf2f9;
        }
        
        .left-side {
            display: flex;
            align-items: center;
            padding-left: 20px;
        }
        
        .navbar-title {
            font-weight: bold;
            font-size: 18px;
            color: #444;
            margin: 0;
            margin-left: 10px;
        }
        
        .main-wrapper {
            display: flex;
            flex: 1;
            justify-content: center;
            align-items: center;
        }
        
        .login-container {
            width: 400px;
            padding: 30px;
            background-color: white;
            border-radius: 10px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
        }
        
        .login-header {
            text-align: center;
            margin-bottom: 30px;
        }
        
        .login-title {
            font-size: 22px;
            font-weight: bold;
            color: #444;
            margin-bottom: 10px;
        }
        
        .login-subtitle {
            font-size: 14px;
            color: #6c757d;
        }
        
        .form-group {
            margin-bottom: 20px;
        }
        
        .form-label {
            display: block;
            margin-bottom: 6px;
            font-size: 14px;
            color: #444;
            font-weight: 500;
        }
        
        .form-control {
            width: 100%;
            padding: 12px;
            border: 1px solid #d0d7de;
            border-radius: 6px;
            font-size: 14px;
            transition: border-color 0.2s;
        }
        
        .form-control:focus {
            outline: none;
            border-color: #6e4ff6;
            box-shadow: 0 0 0 3px rgba(110, 79, 246, 0.1);
        }
        
        .btn {
            display: block;
            width: 50%;
            padding: 5px;
            border-radius: 6px;
            font-size: 16px;
            font-weight: 500;
            cursor: pointer;
            border: none;
            transition: all 0.2s;
            text-align: center;
        }
        
        .btn-primary {
            background-color: #6e4ff6;
            color: white;
        }
        
        .btn-primary:hover {
            background-color: #5d3fd5;
        }
        
        .login-footer {
            text-align: center;
            margin-top: 25px;
            font-size: 14px;
            color: #6c757d;
        }
        
        .login-help {
            margin-top: 15px;
            text-align: center;
        }
        
        .login-help a {
            color: #6e4ff6;
            text-decoration: none;
        }
        
        .login-help a:hover {
            text-decoration: underline;
        }
        
        .remember-me {
            display: flex;
            align-items: center;
            margin-bottom: 20px;
        }
        
        .remember-me input {
            margin-right: 8px;
        }
        
        .footer {
            text-align: center;
            padding: 20px;
            color: #6c757d;
            font-size: 12px;
            background-color: white;
            border-top: 1px solid #edf2f9;
        }
    </style>
</head>
<body>
    <div class="navbar">
        <div class="left-side">
            <img alt="logo" src="https://via.placeholder.com/32" style="width: 32px; height: 32px" />
            <h5 class="navbar-title">医学影像诊断系统</h5>
        </div>
    </div>
    
    <div class="main-wrapper">
        <div class="login-container">
            <div class="login-header">
                <div class="login-title">用户登录</div>

            </div>
            
            <form>
                <div class="form-group">
                    <label class="form-label">用户名</label>
                    <input type="text" class="form-control" placeholder="请输入用户名" required>
                </div>
                
                <div class="form-group">
                    <label class="form-label">密码</label>
                    <input type="password" class="form-control" placeholder="请输入密码" required>
                </div>
                
                <div class="remember-me">
                    <input type="checkbox" id="remember" name="remember">
                    <label for="remember">记住我</label>
                </div>
                
                <div style="display: flex; justify-content: center;">
                    <button type="submit" class="btn btn-primary">登录</button>
                </div>
            </form>
            
        </div>
    </div>
    
    <div class="footer">
        © 2025 云南省毒品依赖戒治技术创新中心 版权所有
    </div>
    
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const form = document.querySelector('form');
            form.addEventListener('submit', function(e) {
                e.preventDefault();
                const username = form.querySelector('input[type="text"]').value;
                const password = form.querySelector('input[type="password"]').value;
                
                // 这里可以添加登录验证逻辑
                console.log('用户名:', username, '密码:', password);
                
                // 登录成功后跳转到主页
                window.location.href = 'DiagnosisDetail.html';
            });
        });
    </script>
</body>
</html> 