<template>
  <div class="min-h-screen bg-gray-100">
    <Navbar />

    <div class="main-wrapper">
      <div class="main-content w-4/5 mx-auto px-4 sm:px-6 lg:px-8 py-10">
        <div v-if="loading" class="text-center py-10">
          <p class="text-lg">加载中...</p>
        </div>
        <div class="header">
          <div class="header-actions flex space-x-4">
            <button
              @click="goBack"
              class="btn btn-secondary px-4 py-2 bg-gray-200 text-gray-700 rounded-md hover:bg-gray-300"
            >
              返回列表
            </button>
          </div>
        </div>

        <!-- 个人信息卡片 -->
        <div class="card bg-white shadow overflow-hidden sm:rounded-lg mt-6">
          <div class="card-header px-4 py-3 sm:px-6 border-b border-gray-200">
            <h3 class="text-lg leading-6 font-medium text-gray-900">
              个人信息
            </h3>
          </div>

          <div class="info-table px-4 py-5 sm:p-6">
            <table class="w-full border-collapse border">
              <tbody>
                <tr>
                  <td
                    class="border px-1 py-2 bg-gray-50 w-24 text-center text-gray-500"
                  >
                    患者姓名
                  </td>
                  <td class="border px-4 py-2 w-32">
                    <span class="w-full px-2 py-1">{{ caseData.pinyin }}</span>
                  </td>
                  <td
                    class="border px-1 py-2 bg-gray-50 w-24 text-center text-gray-500"
                  >
                    检查日期
                  </td>
                  <td class="border px-4 py-2 w-32">
                    <span class="w-full px-2 py-1">{{
                      caseData.check_date
                    }}</span>
                  </td>
                  <td
                    class="border px-1 py-2 bg-gray-50 w-24 text-center text-gray-500"
                  >
                    性别
                  </td>
                  <td class="border px-4 py-2 w-24">
                    <span class="px-2 py-1">{{
                      caseData.gender === "M" ? "男" : "女"
                    }}</span>
                  </td>
                </tr>
                <tr>
                  <td
                    class="border px-1 py-2 bg-gray-50 w-20 text-center text-gray-500"
                  >
                    年龄
                  </td>
                  <td class="border px-4 py-2 w-32">
                    <span class="w-full px-2 py-1">{{ caseData.age }}</span>
                  </td>
                  <td
                    class="border px-1 py-2 bg-gray-50 w-20 text-center text-gray-500"
                  >
                    备注
                  </td>
                  <td class="border px-4 py-2" colspan="3">
                    <span class="w-full px-2 py-1">{{ caseData.remark }}</span>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>

        <!-- 影像资料卡片 -->
        <div class="card bg-white shadow overflow-hidden sm:rounded-lg mt-6">
          <div class="card-header px-4 py-5 sm:px-6 border-b border-gray-200">
            <h3 class="text-lg leading-6 font-medium text-gray-900">
              影像资料
            </h3>
          </div>
          <div class="border-t border-gray-200 px-4 py-5 sm:p-6">
            <div v-if="loading" class="text-center py-10">
              <p class="text-lg">加载中...</p>
            </div>
            <div
              v-else-if="!diagnosisStore.sequences.length"
              class="text-center py-10"
            >
              <p class="text-lg text-gray-500">暂无影像序列</p>
            </div>
            <div v-else>
              <div class="image-viewer-container flex">
                <!-- 左侧序列列表 -->
                <div
                  class="sequence-list w-64 border-r border-gray-200 pr-4 overflow-y-auto bg-gray-50"
                  style="height: 480px"
                >
                  <div
                    v-for="(sequence, i) in diagnosisStore.sequences"
                    :key="i"
                    @click="diagnosisStore.selectSequence(i)"
                    class="sequence-item p-2 mb-2 cursor-move rounded hover:bg-gray-200 transition-colors flex items-center"
                    :class="[
                      diagnosisStore.currentSequenceIndex === i
                        ? 'bg-blue-100 text-blue-600'
                        : 'bg-white',
                      i % 2 === 0 ? 'bg-opacity-80' : 'bg-opacity-60',
                    ]"
                    draggable="true"
                    @dragstart="onDragStart($event, sequence, i)"
                  >
                    <div class="flex items-center justify-between w-full">
                      <div class="flex items-center">
                        <!-- 添加拖拽图标 -->
                        <span class="mr-1 text-gray-400">
                          <svg
                            xmlns="http://www.w3.org/2000/svg"
                            class="h-3 w-3"
                            fill="none"
                            viewBox="0 0 24 24"
                            stroke="currentColor"
                          >
                            <path
                              stroke-linecap="round"
                              stroke-linejoin="round"
                              stroke-width="2"
                              d="M4 6h16M4 12h16M4 18h16"
                            />
                          </svg>
                        </span>
                        <!-- 视频序列标识 -->
                        <span
                          v-if="
                            sequence.images &&
                            sequence.images.length > 0 &&
                            isVideoFile(sequence.images[0])
                          "
                          class="mr-1 text-xs text-purple-600"
                        >
                          <svg
                            xmlns="http://www.w3.org/2000/svg"
                            class="h-3 w-3"
                            fill="none"
                            viewBox="0 0 24 24"
                            stroke="currentColor"
                          >
                            <path
                              stroke-linecap="round"
                              stroke-linejoin="round"
                              stroke-width="2"
                              d="M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z"
                            />
                          </svg>
                        </span>
                        <!-- 图像序列标识 -->
                        <span
                          v-else-if="
                            sequence.images && sequence.images.length > 0
                          "
                          class="mr-1 text-xs text-blue-600"
                        >
                          <svg
                            xmlns="http://www.w3.org/2000/svg"
                            class="h-3 w-3"
                            fill="none"
                            viewBox="0 0 24 24"
                            stroke="currentColor"
                          >
                            <path
                              stroke-linecap="round"
                              stroke-linejoin="round"
                              stroke-width="2"
                              d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"
                            />
                          </svg>
                        </span>
                        <span class="text-xs truncate max-w-40">{{
                          sequence.name
                        }}</span>
                      </div>
                      <span class="text-xs text-gray-500 ml-1"
                        >{{ i + 1 }}/{{ diagnosisStore.sequences.length }}</span
                      >
                    </div>
                    <!-- 添加视频说明标签 -->
                    <div
                      v-if="
                        sequence.images &&
                        sequence.images.length > 0 &&
                        isVideoFile(sequence.images[0])
                      "
                      class="text-xs text-purple-600 mt-1"
                    >
                      视频文件
                    </div>
                  </div>
                </div>

                <!-- 右侧图像显示区域 -->
                <div class="image-panes flex-1 flex space-x-4 ml-4">
                  <!-- 左侧图像窗格 -->
                  <div
                    class="image-pane flex-1 border rounded-lg p-4 bg-white"
                    :class="{
                      'border-blue-400 border-2 bg-blue-50': leftPaneDragOver,
                    }"
                    @dragover="onDragOver($event, 'left')"
                    @dragleave="onDragLeave($event, 'left')"
                    @drop="onDrop($event, 'left')"
                  >
                    <div
                      class="pane-header mb-4 flex items-center justify-between"
                    >
                      <h4 class="text-lg font-medium">
                        {{
                          leftPaneSequence ? leftPaneSequence.name : "窗格 1"
                        }}
                      </h4>
                      <button
                        v-if="leftPaneImage && !isVideoFile(leftPaneImage)"
                        @click="zoomImage('left')"
                        class="p-1 rounded-full hover:bg-gray-100 transition-colors"
                        title="放大图像"
                      >
                        <svg
                          xmlns="http://www.w3.org/2000/svg"
                          class="h-5 w-5 text-gray-500"
                          fill="none"
                          viewBox="0 0 24 24"
                          stroke="currentColor"
                        >
                          <path
                            stroke-linecap="round"
                            stroke-linejoin="round"
                            stroke-width="2"
                            d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0zM10 7v6m3-3H7"
                          />
                        </svg>
                      </button>
                    </div>
                    <div class="image-display">
                      <div
                        class="image-wrapper relative border rounded-lg overflow-hidden"
                        @wheel="handleMouseWheel('left', $event)"
                        style="cursor: ns-resize; width: 100%; margin: 0 auto"
                      >
                        <!-- 图像显示 -->
                        <img
                          v-if="leftPaneImage && !isVideoFile(leftPaneImage)"
                          :src="leftPaneImage"
                          :alt="`序列 ${leftPaneSequenceIndex + 1} 的图像 ${
                            leftPaneImageIndex + 1
                          }`"
                          :style="leftPaneImageStyle"
                        />
                        <!-- 视频显示 -->
                        <div
                          v-else-if="
                            leftPaneImage && isVideoFile(leftPaneImage)
                          "
                          class="relative"
                        >
                          <!-- 加载状态指示器 -->
                          <div
                            v-if="leftVideoLoading"
                            class="absolute inset-0 flex items-center justify-center bg-black bg-opacity-30 z-10"
                          >
                            <div class="text-white">
                              <svg
                                class="animate-spin -ml-1 mr-2 h-8 w-8 text-white inline-block"
                                xmlns="http://www.w3.org/2000/svg"
                                fill="none"
                                viewBox="0 0 24 24"
                              >
                                <circle
                                  class="opacity-25"
                                  cx="12"
                                  cy="12"
                                  r="10"
                                  stroke="currentColor"
                                  stroke-width="4"
                                ></circle>
                                <path
                                  class="opacity-75"
                                  fill="currentColor"
                                  d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                                ></path>
                              </svg>
                              <span class="ml-2">视频加载中...</span>
                            </div>
                          </div>
                          <video
                            :src="leftPaneImage"
                            controls
                            class="w-full h-auto"
                            style="max-height: 400px"
                            @error="handleVideoError($event, 'left')"
                            @loadstart="handleVideoLoadStart('left')"
                            @loadeddata="handleVideoLoaded('left')"
                          ></video>
                        </div>
                        <div
                          v-else
                          class="w-full h-64 flex items-center justify-center bg-gray-100 rounded-lg"
                          :class="{ 'bg-blue-50': leftPaneDragOver }"
                        >
                          <div class="text-center">
                            <svg
                              xmlns="http://www.w3.org/2000/svg"
                              class="h-12 w-12 mx-auto text-gray-400 mb-2"
                              fill="none"
                              viewBox="0 0 24 24"
                              stroke="currentColor"
                            >
                              <path
                                stroke-linecap="round"
                                stroke-linejoin="round"
                                stroke-width="2"
                                d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-8l-4-4m0 0L8 8m4-4v12"
                              />
                            </svg>
                            <p class="text-gray-500">
                              请从左侧列表拖拽序列到此处显示
                            </p>
                          </div>
                        </div>
                      </div>
                    </div>
                    <!-- 添加图像控制区域 -->
                    <div
                      class="image-controls mt-4 space-y-2"
                      v-if="leftPaneImage && !isVideoFile(leftPaneImage)"
                    >
                      <div class="flex space-x-2">
                        <button
                          @click="toggleHorizontalFlip('left')"
                          class="px-2 py-1 bg-gray-200 text-gray-700 rounded-md hover:bg-gray-300 text-xs flex items-center"
                        >
                          <svg
                            class="h-4 w-4 mr-1"
                            viewBox="0 0 24 24"
                            fill="none"
                            xmlns="http://www.w3.org/2000/svg"
                          >
                            <path
                              d="M12 3V21M3 12H21M21 12L17 8M21 12L17 16M12 21L8 17M12 21L16 17M3 12L7 8M3 12L7 16"
                              stroke="currentColor"
                              stroke-width="2"
                              stroke-linecap="round"
                              stroke-linejoin="round"
                            />
                          </svg>
                          水平翻转
                        </button>
                        <button
                          @click="toggleVerticalFlip('left')"
                          class="px-2 py-1 bg-gray-200 text-gray-700 rounded-md hover:bg-gray-300 text-xs flex items-center"
                        >
                          <svg
                            class="h-4 w-4 mr-1"
                            viewBox="0 0 24 24"
                            fill="none"
                            xmlns="http://www.w3.org/2000/svg"
                          >
                            <path
                              d="M3 12H21M12 3V21M8 3H16M8 21H16"
                              stroke="currentColor"
                              stroke-width="2"
                              stroke-linecap="round"
                              stroke-linejoin="round"
                            />
                          </svg>
                          垂直翻转
                        </button>
                        <button
                          @click="resetImageTransforms('left')"
                          class="px-2 py-1 bg-gray-200 text-gray-700 rounded-md hover:bg-gray-300 text-xs flex items-center"
                        >
                          <svg
                            class="h-4 w-4 mr-1"
                            viewBox="0 0 24 24"
                            fill="none"
                            xmlns="http://www.w3.org/2000/svg"
                          >
                            <path
                              d="M2 12C2 6.47715 6.47715 2 12 2C17.5228 2 22 6.47715 22 12C22 17.5228 17.5228 22 12 22C6.47715 22 2 17.5228 2 12Z"
                              stroke="currentColor"
                              stroke-width="2"
                            />
                            <path
                              d="M8 12L16 12"
                              stroke="currentColor"
                              stroke-width="2"
                              stroke-linecap="round"
                              stroke-linejoin="round"
                            />
                            <path
                              d="M12 8L12 16"
                              stroke="currentColor"
                              stroke-width="2"
                              stroke-linecap="round"
                              stroke-linejoin="round"
                            />
                          </svg>
                          重置
                        </button>
                      </div>
                      <div class="contrast-control flex items-center">
                        <label
                          class="text-xs text-gray-500 mr-2 whitespace-nowrap"
                          >对比度: {{ leftPaneContrast }}%</label
                        >
                        <input
                          type="range"
                          v-model.number="leftPaneContrast"
                          min="50"
                          max="200"
                          step="10"
                          class="flex-grow h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer"
                        />
                      </div>
                    </div>
                    <div
                      class="image-controls mt-4"
                      v-if="
                        leftPaneSequence &&
                        leftPaneSequence.images &&
                        leftPaneSequence.images.length > 1
                      "
                    >
                      <div class="slider-container">
                        <input
                          type="range"
                          v-model.number="leftPaneImageIndex"
                          :min="0"
                          :max="(leftPaneSequence?.images?.length || 1) - 1"
                          class="w-full"
                        />
                      </div>
                    </div>
                  </div>

                  <!-- 右侧图像窗格 -->
                  <div
                    class="image-pane flex-1 border rounded-lg p-4 bg-white"
                    :class="{
                      'border-blue-400 border-2 bg-blue-50': rightPaneDragOver,
                    }"
                    @dragover="onDragOver($event, 'right')"
                    @dragleave="onDragLeave($event, 'right')"
                    @drop="onDrop($event, 'right')"
                  >
                    <div
                      class="pane-header mb-4 flex items-center justify-between"
                    >
                      <h4 class="text-lg font-medium">
                        {{
                          rightPaneSequence ? rightPaneSequence.name : "窗格 2"
                        }}
                      </h4>
                      <button
                        v-if="rightPaneImage && !isVideoFile(rightPaneImage)"
                        @click="zoomImage('right')"
                        class="p-1 rounded-full hover:bg-gray-100 transition-colors"
                        title="放大图像"
                      >
                        <svg
                          xmlns="http://www.w3.org/2000/svg"
                          class="h-5 w-5 text-gray-500"
                          fill="none"
                          viewBox="0 0 24 24"
                          stroke="currentColor"
                        >
                          <path
                            stroke-linecap="round"
                            stroke-linejoin="round"
                            stroke-width="2"
                            d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0zM10 7v6m3-3H7"
                          />
                        </svg>
                      </button>
                    </div>
                    <div class="image-display">
                      <div
                        class="image-wrapper relative border rounded-lg overflow-hidden"
                        @wheel="handleMouseWheel('right', $event)"
                        style="cursor: ns-resize; width: 100%; margin: 0 auto"
                      >
                        <!-- 图像显示 -->
                        <img
                          v-if="rightPaneImage && !isVideoFile(rightPaneImage)"
                          :src="rightPaneImage"
                          :alt="`序列 ${rightPaneSequenceIndex + 1} 的图像 ${
                            rightPaneImageIndex + 1
                          }`"
                          :style="rightPaneImageStyle"
                        />
                        <!-- 视频显示 -->
                        <div
                          v-else-if="
                            rightPaneImage && isVideoFile(rightPaneImage)
                          "
                          class="relative"
                        >
                          <!-- 加载状态指示器 -->
                          <div
                            v-if="rightVideoLoading"
                            class="absolute inset-0 flex items-center justify-center bg-black bg-opacity-30 z-10"
                          >
                            <div class="text-white">
                              <svg
                                class="animate-spin -ml-1 mr-2 h-8 w-8 text-white inline-block"
                                xmlns="http://www.w3.org/2000/svg"
                                fill="none"
                                viewBox="0 0 24 24"
                              >
                                <circle
                                  class="opacity-25"
                                  cx="12"
                                  cy="12"
                                  r="10"
                                  stroke="currentColor"
                                  stroke-width="4"
                                ></circle>
                                <path
                                  class="opacity-75"
                                  fill="currentColor"
                                  d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                                ></path>
                              </svg>
                              <span class="ml-2">视频加载中...</span>
                            </div>
                          </div>
                          <video
                            :src="rightPaneImage"
                            controls
                            class="w-full h-auto"
                            style="max-height: 400px"
                            @error="handleVideoError($event, 'right')"
                            @loadstart="handleVideoLoadStart('right')"
                            @loadeddata="handleVideoLoaded('right')"
                          ></video>
                        </div>
                        <div
                          v-else
                          class="w-full h-64 flex items-center justify-center bg-gray-100 rounded-lg"
                          :class="{ 'bg-blue-50': rightPaneDragOver }"
                        >
                          <div class="text-center">
                            <svg
                              xmlns="http://www.w3.org/2000/svg"
                              class="h-12 w-12 mx-auto text-gray-400 mb-2"
                              fill="none"
                              viewBox="0 0 24 24"
                              stroke="currentColor"
                            >
                              <path
                                stroke-linecap="round"
                                stroke-linejoin="round"
                                stroke-width="2"
                                d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-8l-4-4m0 0L8 8m4-4v12"
                              />
                            </svg>
                            <p class="text-gray-500">
                              请从左侧列表拖拽序列到此处显示
                            </p>
                          </div>
                        </div>
                      </div>
                    </div>
                    <!-- 添加图像控制区域 -->
                    <div
                      class="image-controls mt-4 space-y-2"
                      v-if="rightPaneImage && !isVideoFile(rightPaneImage)"
                    >
                      <div class="flex space-x-2">
                        <button
                          @click="toggleHorizontalFlip('right')"
                          class="px-2 py-1 bg-gray-200 text-gray-700 rounded-md hover:bg-gray-300 text-xs flex items-center"
                        >
                          <svg
                            class="h-4 w-4 mr-1"
                            viewBox="0 0 24 24"
                            fill="none"
                            xmlns="http://www.w3.org/2000/svg"
                          >
                            <path
                              d="M12 3V21M3 12H21M21 12L17 8M21 12L17 16M12 21L8 17M12 21L16 17M3 12L7 8M3 12L7 16"
                              stroke="currentColor"
                              stroke-width="2"
                              stroke-linecap="round"
                              stroke-linejoin="round"
                            />
                          </svg>
                          水平翻转
                        </button>
                        <button
                          @click="toggleVerticalFlip('right')"
                          class="px-2 py-1 bg-gray-200 text-gray-700 rounded-md hover:bg-gray-300 text-xs flex items-center"
                        >
                          <svg
                            class="h-4 w-4 mr-1"
                            viewBox="0 0 24 24"
                            fill="none"
                            xmlns="http://www.w3.org/2000/svg"
                          >
                            <path
                              d="M3 12H21M12 3V21M8 3H16M8 21H16"
                              stroke="currentColor"
                              stroke-width="2"
                              stroke-linecap="round"
                              stroke-linejoin="round"
                            />
                          </svg>
                          垂直翻转
                        </button>
                        <button
                          @click="resetImageTransforms('right')"
                          class="px-2 py-1 bg-gray-200 text-gray-700 rounded-md hover:bg-gray-300 text-xs flex items-center"
                        >
                          <svg
                            class="h-4 w-4 mr-1"
                            viewBox="0 0 24 24"
                            fill="none"
                            xmlns="http://www.w3.org/2000/svg"
                          >
                            <path
                              d="M2 12C2 6.47715 6.47715 2 12 2C17.5228 2 22 6.47715 22 12C22 17.5228 17.5228 22 12 22C6.47715 22 2 17.5228 2 12Z"
                              stroke="currentColor"
                              stroke-width="2"
                            />
                            <path
                              d="M8 12L16 12"
                              stroke="currentColor"
                              stroke-width="2"
                              stroke-linecap="round"
                              stroke-linejoin="round"
                            />
                            <path
                              d="M12 8L12 16"
                              stroke="currentColor"
                              stroke-width="2"
                              stroke-linecap="round"
                              stroke-linejoin="round"
                            />
                          </svg>
                          重置
                        </button>
                      </div>
                      <div class="contrast-control flex items-center">
                        <label
                          class="text-xs text-gray-500 mr-2 whitespace-nowrap"
                          >对比度: {{ rightPaneContrast }}%</label
                        >
                        <input
                          type="range"
                          v-model.number="rightPaneContrast"
                          min="50"
                          max="200"
                          step="10"
                          class="flex-grow h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer"
                        />
                      </div>
                    </div>
                    <div
                      class="image-controls mt-4"
                      v-if="
                        rightPaneSequence &&
                        rightPaneSequence.images &&
                        rightPaneSequence.images.length > 1
                      "
                    >
                      <div class="slider-container">
                        <input
                          type="range"
                          v-model.number="rightPaneImageIndex"
                          :min="0"
                          :max="(rightPaneSequence?.images?.length || 1) - 1"
                          class="w-full"
                        />
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 诊断结果卡片 -->
        <div class="card bg-white shadow overflow-hidden sm:rounded-lg mt-6">
          <div class="card-header px-4 py-5 sm:px-6 border-b border-gray-200">
            <h3 class="text-lg leading-6 font-medium text-gray-900">
              诊断结果
            </h3>
          </div>
          <div class="border-t border-gray-200 px-4 py-5 sm:p-6">
            <div class="form-group">
              <textarea
                id="diagnosis"
                rows="5"
                v-model="caseData.result"
                class="w-full px-3 py-2 border border-gray-300 rounded-md"
                placeholder="请输入诊断结果..."
                :disabled="diagnosisIsSubmitted"
              ></textarea>
            </div>
            <div
              v-if="diagnosisIsSubmitted"
              class="mt-2 p-2 bg-yellow-50 text-yellow-700 rounded"
            >
              该诊断结果已提交，不可再编辑
            </div>
            <div
              v-if="saveSuccess"
              class="mt-2 p-2 bg-green-50 text-green-700 rounded"
            >
              {{ saveStatusMessage }}
              <span v-if="redirectCountdown > 0" class="ml-2 font-semibold"
                >({{ redirectCountdown }}秒)</span
              >
            </div>
            <div v-if="error" class="mt-2 p-2 bg-red-50 text-red-700 rounded">
              {{ error }}
            </div>
            <div
              class="card-footer flex justify-end space-x-4 mt-6 pt-4 border-t border-gray-200"
            >
              <button
                @click="clearDiagnosis"
                class="btn bg-gray-300 text-gray-700 px-4 py-2 rounded-md hover:bg-gray-400"
                :disabled="diagnosisIsSubmitted"
              >
                清空
              </button>
              <button
                @click="saveDraft"
                class="btn px-4 py-2 bg-gray-500 text-white rounded-md hover:bg-gray-600"
                :disabled="loading || diagnosisIsSubmitted"
              >
                <span v-if="loading && savingMode === 'draft'">暂存中...</span>
                <span v-else>暂存</span>
              </button>
              <button
                @click="submitDiagnosis"
                class="btn btn-primary px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
                :disabled="loading || diagnosisIsSubmitted"
              >
                <span v-if="loading && savingMode === 'submit'">提交中...</span>
                <span v-else>提交</span>
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 添加放大图像模态框 -->
    <div
      v-if="zoomedImage"
      class="fixed inset-0 bg-black bg-opacity-75 flex items-center justify-center z-50"
      @click="zoomedImage = null"
    >
      <div
        class="relative max-w-4xl w-full h-full flex items-center justify-center"
      >
        <img
          :src="zoomedImage"
          class="max-w-full max-h-full object-contain"
          @click.stop
        />
        <button
          @click="zoomedImage = null"
          class="absolute top-4 right-4 p-2 bg-white rounded-full shadow-lg hover:bg-gray-100"
        >
          <svg
            xmlns="http://www.w3.org/2000/svg"
            class="h-6 w-6"
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
          >
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              stroke-width="2"
              d="M6 18L18 6M6 6l12 12"
            />
          </svg>
        </button>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import { defineComponent, onMounted, ref, computed, watch } from "vue";
import { useRoute, useRouter } from "vue-router";
import { useDiagnosisStore } from "@/store/diagnosis";
import { useAuthStore } from "@/store/auth";
import Navbar from "@/components/common/Navbar.vue";
import { DiagnosisResult } from "@/services/diagnosis";

export default defineComponent({
  name: "DiagnosisView",
  components: {
    Navbar,
  },
  setup() {
    const router = useRouter();
    const route = useRoute();
    const diagnosisStore = useDiagnosisStore();
    const authStore = useAuthStore();

    const caseId = computed(() => {
      const subject_id = route.params.subject_id;
      return subject_id ? (subject_id as string) : null;
    });

    const isNewCase = computed(() => !caseId.value);

    const isLoading = ref(false);
    const loading = computed(() => diagnosisStore.loading || isLoading.value);
    const errorMessage = ref("");
    const error = computed(() => diagnosisStore.error || errorMessage.value);
    const saveSuccess = ref(false);
    const saveStatusMessage = ref("");
    const savingMode = ref<"draft" | "submit">("draft");
    const existingDiagnosis = ref<DiagnosisResult | null>(null);
    const redirectCountdown = ref(0);

    const caseData = ref<Partial<DiagnosisResult>>({
      subject_id: "",
      pinyin: "",
      age: 0,
      gender: "",
      check_date: "",
      region: "",
      submit_time: null,
      submit_user: null,
      result: "",
      status: "pending",
      remark: null,
    });

    // 图像变换状态 - 保留在组件中，因为这是UI状态
    const flipHorizontal = ref(false);
    const flipVertical = ref(false);
    const rotation = ref(0);
    const contrast = ref(100);

    // 计算应用变换后的CSS样式
    const imageStyle = computed(() => {
      return {
        transform: `
          ${flipHorizontal.value ? "scaleX(-1)" : ""}
          ${flipVertical.value ? "scaleY(-1)" : ""}
          rotate(${rotation.value}deg)
        `,
        filter: `contrast(${contrast.value}%)`,
        width: "400px",
        height: "auto",
        objectFit: "contain",
      };
    });

    // 当前检查部位
    const currentRegion = ref<string>("head");

    // 获取当前部位的检查记录
    const getCheckForRegion = (region: string) => {
      if (
        !diagnosisStore.currentSubject?.checks ||
        !Array.isArray(diagnosisStore.currentSubject.checks)
      )
        return null;
      return (
        diagnosisStore.currentSubject.checks.find(
          (check) => check.region === region
        ) || null
      );
    };

    // 获取当前部位的检查状态
    const getCheckStatusForRegion = (region: string) => {
      const check = getCheckForRegion(region);
      return check ? check.check_status : null;
    };

    // 获取当前部位的诊断状态
    const getDiagnosisStatusForRegion = (region: string) => {
      const check = getCheckForRegion(region);
      return check ? check.diagnosis_status : "pending";
    };

    // 替换原有的诊断状态判断
    const diagnosisIsSubmitted = computed(() => {
      // 首先从当前的DiagnosisResult中判断
      if (caseData.value.status === "submitted") {
        return true;
      }
      // 如果DiagnosisResult中没有明确状态，则从Subject的checks中获取
      return getDiagnosisStatusForRegion(currentRegion.value) === "submitted";
    });

    // 添加新的状态变量
    const leftPaneSequenceIndex = ref(-1);
    const leftPaneImageIndex = ref(0);
    const leftPaneFlipHorizontal = ref(false);
    const leftPaneFlipVertical = ref(false);
    const leftPaneRotation = ref(0);
    const leftPaneContrast = ref(100);

    const rightPaneSequenceIndex = ref(-1);
    const rightPaneImageIndex = ref(0);
    const rightPaneFlipHorizontal = ref(false);
    const rightPaneFlipVertical = ref(false);
    const rightPaneRotation = ref(0);
    const rightPaneContrast = ref(100);

    // 计算属性
    const leftPaneSequence = computed(() => {
      return leftPaneSequenceIndex.value >= 0
        ? diagnosisStore.sequences[leftPaneSequenceIndex.value]
        : null;
    });

    const rightPaneSequence = computed(() => {
      return rightPaneSequenceIndex.value >= 0
        ? diagnosisStore.sequences[rightPaneSequenceIndex.value]
        : null;
    });

    const leftPaneImage = computed(() => {
      return leftPaneSequence.value?.images?.[leftPaneImageIndex.value];
    });

    const rightPaneImage = computed(() => {
      return rightPaneSequence.value?.images?.[rightPaneImageIndex.value];
    });

    const leftPaneImageStyle = computed(() => {
      return {
        transform: `
          ${leftPaneFlipHorizontal.value ? "scaleX(-1)" : ""}
          ${leftPaneFlipVertical.value ? "scaleY(-1)" : ""}
          rotate(${leftPaneRotation.value}deg)
        `,
        filter: `contrast(${leftPaneContrast.value}%)`,
        width: "100%",
        height: "auto",
        objectFit: "contain",
      };
    });

    const rightPaneImageStyle = computed(() => {
      return {
        transform: `
          ${rightPaneFlipHorizontal.value ? "scaleX(-1)" : ""}
          ${rightPaneFlipVertical.value ? "scaleY(-1)" : ""}
          rotate(${rightPaneRotation.value}deg)
        `,
        filter: `contrast(${rightPaneContrast.value}%)`,
        width: "100%",
        height: "auto",
        objectFit: "contain",
      };
    });

    const zoomedImage = ref<string | null>(null);

    // 在setup函数中添加视频加载状态处理函数
    const leftVideoLoading = ref(false);
    const rightVideoLoading = ref(false);

    function handleVideoLoadStart(pane: "left" | "right") {
      if (pane === "left") {
        leftVideoLoading.value = true;
      } else {
        rightVideoLoading.value = true;
      }
    }

    function handleVideoLoaded(pane: "left" | "right") {
      if (pane === "left") {
        leftVideoLoading.value = false;
      } else {
        rightVideoLoading.value = false;
      }
    }

    // 增加拖拽状态
    const leftPaneDragOver = ref(false);
    const rightPaneDragOver = ref(false);

    onMounted(async () => {
      // Verify authentication before loading data
      if (!authStore.token) {
        router.push("/login");
        return;
      }

      if (caseId.value) {
        try {
          isLoading.value = true;
          await diagnosisStore.loadSubjectById(caseId.value);

          // 设置当前部位(从URL参数获取或默认为head)
          currentRegion.value = (route.query.region as string) || "head";

          // 加载诊断结果 - 不再检查当前部位状态，直接加载诊断结果
          await diagnosisStore.loadDiagnosisResult(
            caseId.value,
            currentRegion.value
          );

          // 如果有诊断结果，显示
          if (diagnosisStore.currentDiagnosis) {
            existingDiagnosis.value = diagnosisStore.currentDiagnosis;
            // 使用诊断结果来填充caseData
            caseData.value = {
              ...diagnosisStore.currentDiagnosis,
            };
          } else if (diagnosisStore.currentSubject) {
            // 如果没有诊断结果但有患者信息，则使用患者信息初始化caseData
            caseData.value = {
              subject_id: diagnosisStore.currentSubject.subject_id,
              name: diagnosisStore.currentSubject.name,
              pinyin: diagnosisStore.currentSubject.pinyin,
              age: diagnosisStore.currentSubject.age,
              gender: diagnosisStore.currentSubject.gender,
              check_date: diagnosisStore.currentSubject.check_date,
              region: currentRegion.value,
              result: "",
              status: "pending",
              remark: diagnosisStore.currentSubject.remark,
            };
          }

          // 加载图像序列
          await diagnosisStore.loadImageSequences(
            caseId.value,
            currentRegion.value
          );

          // 优化：预加载前几个序列的图像，但不自动放置到窗格中
          if (diagnosisStore.sequences.length > 0) {
            // 不再默认选择序列
            leftPaneSequenceIndex.value = -1;
            leftPaneImageIndex.value = 0;
            rightPaneSequenceIndex.value = -1;
            rightPaneImageIndex.value = 0;

            // 仍然预加载前几个序列的图像，提高拖放时的响应速度
            const preloadPromises = [];
            for (
              let i = 0;
              i < Math.min(3, diagnosisStore.sequences.length);
              i++
            ) {
              preloadPromises.push(diagnosisStore.preloadImages(i, 0));
            }

            // 等待预加载完成
            Promise.all(preloadPromises).then(() => {
              console.log("初始序列预加载完成");
            });
          }
        } catch (err) {
          console.error("Failed to load subject data:", err);
          errorMessage.value = "加载患者数据失败，请检查网络连接或重新登录";
        } finally {
          isLoading.value = false;
        }
      } else {
        isLoading.value = false;
      }

      // 确保页面加载完成后滚动到顶部
      window.scrollTo({
        top: 0,
        left: 0,
        behavior: "auto",
      });
    });

    // 添加对leftPaneSequenceIndex和rightPaneSequenceIndex变化的监听
    watch(
      [leftPaneSequenceIndex, rightPaneSequenceIndex],
      ([newLeftIndex, newRightIndex]) => {
        // 当序列改变时，预加载新选择的序列的图像
        if (newLeftIndex >= 0 && diagnosisStore.sequences[newLeftIndex]) {
          diagnosisStore.preloadImages(newLeftIndex, leftPaneImageIndex.value);
        }

        if (newRightIndex >= 0 && diagnosisStore.sequences[newRightIndex]) {
          diagnosisStore.preloadImages(
            newRightIndex,
            rightPaneImageIndex.value
          );
        }

        // 防止因序列变化导致页面滚动
        setTimeout(() => {
          // 使用 requestAnimationFrame 确保在下一帧渲染前执行
          requestAnimationFrame(() => {
            // 如果滚动位置明显偏下，重置到顶部
            if (window.scrollY > 200) {
              window.scrollTo({ top: 0, behavior: "auto" });
            }
          });
        }, 50);
      }
    );

    // 添加对leftPaneImageIndex和rightPaneImageIndex变化的监听
    watch(
      [leftPaneImageIndex, rightPaneImageIndex],
      ([newLeftImageIndex, newRightImageIndex]) => {
        // 当图像索引改变时，预加载
        if (leftPaneSequenceIndex.value >= 0) {
          diagnosisStore.preloadImages(
            leftPaneSequenceIndex.value,
            newLeftImageIndex
          );
        }

        if (rightPaneSequenceIndex.value >= 0) {
          diagnosisStore.preloadImages(
            rightPaneSequenceIndex.value,
            newRightImageIndex
          );
        }
      }
    );

    // 添加对currentImageIndex变化的监听，以触发预加载
    watch(
      () => diagnosisStore.currentImageIndex,
      (newIndex) => {
        // 预加载当前图像和前后两张图像
        diagnosisStore.preloadImages(
          diagnosisStore.currentSequenceIndex,
          newIndex
        );
      }
    );

    function clearDiagnosis() {
      if (diagnosisIsSubmitted.value) {
        errorMessage.value = "该诊断结果已提交，不可清空";
        return;
      }
      caseData.value.result = "";
      saveSuccess.value = false;
      saveStatusMessage.value = "";
      errorMessage.value = "";
    }

    // 暂存诊断结果
    async function saveDraft() {
      return await saveDiagnosisResult("draft");
    }

    // 提交诊断结果
    async function submitDiagnosis() {
      return await saveDiagnosisResult("submitted");
    }

    // 保存诊断结果
    async function saveDiagnosisResult(status: string) {
      // 检查是否已提交
      if (diagnosisIsSubmitted.value) {
        errorMessage.value = "该诊断结果已提交，不可修改";
        return false;
      }

      // Reset messages
      saveSuccess.value = false;
      saveStatusMessage.value = "";
      errorMessage.value = "";
      savingMode.value = status === "draft" ? "draft" : "submit";
      redirectCountdown.value = 0;

      // Verify authentication before saving
      if (!authStore.token) {
        router.push("/login");
        return false;
      }

      // Client-side validation
      if (!caseData.value.result?.trim()) {
        errorMessage.value = "请输入诊断结果";
        return false;
      }

      try {
        isLoading.value = true;
        const diagnosisData = {
          subject_id: caseData.value.subject_id!,
          result: caseData.value.result || "",
          check_date: caseData.value.check_date!,
          region: currentRegion.value, // 使用当前部位而不是route.query
          status: status, // 状态值为 'draft' 或 'submitted'
        };

        // Call the store's updateDiagnosis method
        const success = await diagnosisStore.updateDiagnosis(diagnosisData);

        if (success) {
          saveSuccess.value = true;
          saveStatusMessage.value =
            status === "draft"
              ? "诊断结果已暂存"
              : "诊断结果已成功提交，3秒后将返回列表页...";

          // 更新当前状态
          if (diagnosisStore.currentDiagnosis) {
            caseData.value.status = diagnosisStore.currentDiagnosis.status;
          }

          // 如果是提交，返回dashboard页面
          if (status === "submitted") {
            // 延迟3秒返回，让用户看到提交成功消息
            redirectCountdown.value = 3; // 设置倒计时初始值

            // 创建倒计时间隔
            const countdownInterval = setInterval(() => {
              redirectCountdown.value--;
              if (redirectCountdown.value <= 0) {
                clearInterval(countdownInterval);
              }
            }, 1000);

            setTimeout(() => {
              clearInterval(countdownInterval); // 确保清除间隔
              router.push("/dashboard");
            }, 3000);
          } else {
            // 如果是暂存，重新加载诊断结果
            await diagnosisStore.loadDiagnosisResult(
              caseId.value!,
              currentRegion.value
            );
            if (diagnosisStore.currentDiagnosis) {
              existingDiagnosis.value = diagnosisStore.currentDiagnosis;
            }
          }

          return true;
        } else {
          // Error message is already set in the store
          return false;
        }
      } catch (err: any) {
        console.error("Error saving diagnosis:", err);
        errorMessage.value = err.message || "保存诊断结果失败，请稍后再试";
        return false;
      } finally {
        isLoading.value = false;
      }
    }

    function goBack() {
      // 获取返回URL的查询参数
      const returnTo = route.query.returnTo as string;

      // 从sessionStorage读取返回位置信息
      let returnPosition: any = null;
      try {
        const positionData = sessionStorage.getItem("diagnosisReturnPosition");
        if (positionData) {
          returnPosition = JSON.parse(positionData);

          // 验证数据是否有效（检查时间戳不超过2小时）
          const now = new Date().getTime();
          const isValid =
            returnPosition &&
            returnPosition.timestamp &&
            now - returnPosition.timestamp < 2 * 60 * 60 * 1000;

          if (!isValid) {
            console.log("返回位置信息已过期，使用默认返回逻辑");
            returnPosition = null;
          } else {
            console.log("成功获取返回位置信息:", returnPosition);
          }
        } else {
          console.log("未找到返回位置信息");
        }
      } catch (err) {
        console.error("获取返回位置信息失败:", err);
      }

      // 如果是从仪表盘返回，则保留查询参数
      if (returnTo === "dashboard") {
        // 构建返回路由，保留必要的查询参数
        const query: Record<string, string> = {};

        // 获取区域信息，优先使用保存的位置信息
        const region =
          returnPosition?.region || (route.query.region as string) || "head";
        query.region = region;

        // 获取页码信息，优先使用保存的位置信息
        if (returnPosition?.page && returnPosition.page > 1) {
          query.page = returnPosition.page.toString();
        } else if (route.query.returnPage) {
          query.page = route.query.returnPage as string;
        }

        // 如果有视图信息且不是regions，使用该信息
        if (returnPosition?.view && returnPosition.view !== "regions") {
          query.view = returnPosition.view;
          delete query.region; // 如果不是regions视图，不需要region参数
        }

        // 添加一个时间戳参数，强制刷新状态
        query._t = new Date().getTime().toString();

        // 使用一个特殊的标志来强制重置滚动位置恢复逻辑
        sessionStorage.removeItem("isReturningFromDiagnosis");
        sessionStorage.setItem(
          "lastReturnTime",
          new Date().getTime().toString()
        );

        // 将isReturningFromDiagnosis标志提前设置到sessionStorage
        if (returnPosition) {
          // 确保滚动位置值是有效的（如果之前已经保存）
          if (
            returnPosition.scrollPosition &&
            returnPosition.scrollPosition > 0
          ) {
            console.log("使用已保存的滚动位置:", returnPosition.scrollPosition);
            sessionStorage.setItem("isReturningFromDiagnosis", "true");
          } else {
            // 如果没有有效的滚动位置，尝试从页面中获取
            const currentScroll =
              window.scrollY ||
              document.documentElement.scrollTop ||
              document.body.scrollTop;

            if (currentScroll > 50) {
              // 更新保存的位置信息
              returnPosition.scrollPosition = currentScroll;
              sessionStorage.setItem(
                "diagnosisReturnPosition",
                JSON.stringify(returnPosition)
              );
              console.log("保存当前滚动位置:", currentScroll);
              sessionStorage.setItem("isReturningFromDiagnosis", "true");
            }
          }
        }

        console.log("构建返回URL:", { path: "/dashboard", query });

        // 执行路由跳转
        router.push({
          path: "/dashboard",
          query,
        });
      } else {
        // 默认返回仪表盘
        router.push("/dashboard");
      }
    }

    // 图像操作方法 - 保留在组件中，因为这是UI操作
    function toggleHorizontalFlip(pane: "left" | "right") {
      if (pane === "left") {
        leftPaneFlipHorizontal.value = !leftPaneFlipHorizontal.value;
      } else {
        rightPaneFlipHorizontal.value = !rightPaneFlipHorizontal.value;
      }
    }

    function toggleVerticalFlip(pane: "left" | "right") {
      if (pane === "left") {
        leftPaneFlipVertical.value = !leftPaneFlipVertical.value;
      } else {
        rightPaneFlipVertical.value = !rightPaneFlipVertical.value;
      }
    }

    function rotateImage(pane: "left" | "right") {
      if (pane === "left") {
        leftPaneRotation.value = (leftPaneRotation.value + 90) % 360;
      } else {
        rightPaneRotation.value = (rightPaneRotation.value + 90) % 360;
      }
    }

    function updateContrast(event: Event) {
      const target = event.target as HTMLInputElement;
      contrast.value = parseInt(target.value);
    }

    function resetImageTransforms(pane: "left" | "right") {
      if (pane === "left") {
        leftPaneFlipHorizontal.value = false;
        leftPaneFlipVertical.value = false;
        leftPaneRotation.value = 0;
        leftPaneContrast.value = 100;
      } else {
        rightPaneFlipHorizontal.value = false;
        rightPaneFlipVertical.value = false;
        rightPaneRotation.value = 0;
        rightPaneContrast.value = 100;
      }
    }

    // 拖拽相关方法
    function onDragStart(event: DragEvent, sequence: any, index: number) {
      event.dataTransfer?.setData("sequenceIndex", index.toString());
    }

    function onDragOver(event: DragEvent, pane: "left" | "right") {
      event.preventDefault();
      if (pane === "left") {
        leftPaneDragOver.value = true;
      } else {
        rightPaneDragOver.value = true;
      }
    }

    function onDragLeave(event: DragEvent, pane: "left" | "right") {
      event.preventDefault();
      if (pane === "left") {
        leftPaneDragOver.value = false;
      } else {
        rightPaneDragOver.value = false;
      }
    }

    function onDrop(event: DragEvent, pane: "left" | "right") {
      // 记录当前滚动位置
      const currentScrollY = window.scrollY;

      // 重置拖拽状态
      leftPaneDragOver.value = false;
      rightPaneDragOver.value = false;

      const sequenceIndex = parseInt(
        event.dataTransfer?.getData("sequenceIndex") || "-1"
      );

      if (sequenceIndex < 0) return; // 无效的序列索引

      // 更新序列索引并重置图像索引
      if (pane === "left") {
        leftPaneSequenceIndex.value = sequenceIndex;
        leftPaneImageIndex.value = 0;
      } else {
        rightPaneSequenceIndex.value = sequenceIndex;
        rightPaneImageIndex.value = 0;
      }

      // 预加载图像
      setTimeout(() => {
        if (diagnosisStore.sequences[sequenceIndex]) {
          const sequence = diagnosisStore.sequences[sequenceIndex];
          const totalImages = sequence.images?.length || 0;
          console.log(
            `开始预加载${pane === "left" ? "左" : "右"}侧窗格序列 ${
              sequence.name
            } 的 ${totalImages} 张图像`
          );

          // 优先加载前15张图像
          const preloadCount = Math.min(15, totalImages);
          for (let i = 0; i < preloadCount; i++) {
            // 使用setTimeout避免阻塞UI线程，并给每个预加载操作一小段延迟
            setTimeout(() => {
              diagnosisStore.preloadImages(sequenceIndex, i);
            }, i * 5); // 每张图像错开5ms，避免过度请求阻塞
          }
        }

        // 恢复滚动位置，防止拖放导致页面滚动
        window.scrollTo({ top: currentScrollY, behavior: "auto" });
      }, 0);
    }

    function zoomImage(pane: "left" | "right") {
      // 仅对图像类型执行放大操作
      if (
        pane === "left" &&
        leftPaneImage.value &&
        !isVideoFile(leftPaneImage.value)
      ) {
        zoomedImage.value = leftPaneImage.value;
      } else if (
        pane === "right" &&
        rightPaneImage.value &&
        !isVideoFile(rightPaneImage.value)
      ) {
        zoomedImage.value = rightPaneImage.value;
      }
    }

    // 在setup函数中添加视频错误处理函数
    function handleVideoError(event: Event, pane: "left" | "right") {
      console.error(`视频加载失败 (${pane} 窗格):`, event);
      errorMessage.value = `视频加载失败，请检查网络连接或联系管理员`;
    }

    // 在sequence.name旁边显示标识符(图像/视频)
    function isVideoFile(url: string): boolean {
      if (!url) return false;

      // 检查文件扩展名
      if (
        url.toLowerCase().endsWith(".mp4") ||
        url.toLowerCase().endsWith(".webm") ||
        url.toLowerCase().endsWith(".ogg")
      ) {
        return true;
      }

      // 检查URL中是否包含视频相关字符串（处理带查询参数的URL）
      if (
        url.toLowerCase().includes("video/") ||
        url.toLowerCase().includes("diag_video") ||
        url.toLowerCase().includes(".mp4?")
      ) {
        return true;
      }

      return false;
    }

    // 获取序列类型标签
    function getSequenceTypeLabel(sequence: any): string {
      if (!sequence || !sequence.images || sequence.images.length === 0) {
        return "";
      }
      // 检查第一个文件是否是视频
      return isVideoFile(sequence.images[0]) ? "视频" : "图像";
    }

    // 修改鼠标滚轮事件处理函数，确保不会影响到页面的整体滚动
    function handleMouseWheel(pane: "left" | "right", event: WheelEvent) {
      // 阻止默认滚动行为，防止它引起整个页面的滚动
      event.preventDefault();
      event.stopPropagation();

      // 如果是视频，不进行滚动操作
      if (
        (pane === "left" &&
          leftPaneImage.value &&
          isVideoFile(leftPaneImage.value)) ||
        (pane === "right" &&
          rightPaneImage.value &&
          isVideoFile(rightPaneImage.value))
      ) {
        return;
      }

      // 记录当前滚动位置
      const currentScrollY = window.scrollY;

      // 存储滚动前的索引值
      const oldLeftIndex = leftPaneImageIndex.value;
      const oldRightIndex = rightPaneImageIndex.value;

      // 根据滚动方向和当前窗格更新图像索引
      if (event.deltaY > 0) {
        // 向下滚动
        if (
          pane === "left" &&
          leftPaneImageIndex.value <
            (leftPaneSequence.value?.images?.length || 1) - 1
        ) {
          leftPaneImageIndex.value++;
        } else if (
          pane === "right" &&
          rightPaneImageIndex.value <
            (rightPaneSequence.value?.images?.length || 1) - 1
        ) {
          rightPaneImageIndex.value++;
        }
      } else {
        // 向上滚动
        if (pane === "left" && leftPaneImageIndex.value > 0) {
          leftPaneImageIndex.value--;
        } else if (pane === "right" && rightPaneImageIndex.value > 0) {
          rightPaneImageIndex.value--;
        }
      }

      // 检查索引是否变化，如果变化则触发预加载
      const indexChanged =
        (pane === "left" && oldLeftIndex !== leftPaneImageIndex.value) ||
        (pane === "right" && oldRightIndex !== rightPaneImageIndex.value);

      if (indexChanged) {
        // 预加载当前图像
        setTimeout(() => {
          if (pane === "left") {
            diagnosisStore.preloadImages(
              leftPaneSequenceIndex.value,
              leftPaneImageIndex.value
            );

            // 用户滚动太快时，预加载前后多张图像
            if (Math.abs(oldLeftIndex - leftPaneImageIndex.value) > 2) {
              const rangeStart = Math.max(0, leftPaneImageIndex.value - 5);
              const rangeEnd = Math.min(
                (leftPaneSequence.value?.images?.length || 1) - 1,
                leftPaneImageIndex.value + 10
              );

              for (let i = rangeStart; i <= rangeEnd; i++) {
                if (i !== leftPaneImageIndex.value) {
                  diagnosisStore.preloadImages(leftPaneSequenceIndex.value, i);
                }
              }
            }
          } else {
            diagnosisStore.preloadImages(
              rightPaneSequenceIndex.value,
              rightPaneImageIndex.value
            );

            // 用户滚动太快时，预加载前后多张图像
            if (Math.abs(oldRightIndex - rightPaneImageIndex.value) > 2) {
              const rangeStart = Math.max(0, rightPaneImageIndex.value - 5);
              const rangeEnd = Math.min(
                (rightPaneSequence.value?.images?.length || 1) - 1,
                rightPaneImageIndex.value + 10
              );

              for (let i = rangeStart; i <= rangeEnd; i++) {
                if (i !== rightPaneImageIndex.value) {
                  diagnosisStore.preloadImages(rightPaneSequenceIndex.value, i);
                }
              }
            }
          }

          // 恢复滚动位置
          window.scrollTo({ top: currentScrollY, behavior: "auto" });
        }, 0);
      } else {
        // 即使没有变化，也确保滚动位置不受影响
        window.scrollTo({ top: currentScrollY, behavior: "auto" });
      }
    }

    return {
      caseData,
      isNewCase,
      loading,
      error,
      saveSuccess,
      saveStatusMessage,
      savingMode,
      diagnosisIsSubmitted,
      saveDraft,
      submitDiagnosis,
      clearDiagnosis,
      goBack,
      diagnosisStore,
      // 图像变换状态
      flipHorizontal,
      flipVertical,
      rotation,
      contrast,
      imageStyle,
      // 图像操作方法
      toggleHorizontalFlip,
      toggleVerticalFlip,
      rotateImage,
      updateContrast,
      resetImageTransforms,
      leftPaneSequenceIndex,
      leftPaneImageIndex,
      leftPaneFlipHorizontal,
      leftPaneFlipVertical,
      leftPaneRotation,
      leftPaneContrast,
      rightPaneSequenceIndex,
      rightPaneImageIndex,
      rightPaneFlipHorizontal,
      rightPaneFlipVertical,
      rightPaneRotation,
      rightPaneContrast,
      leftPaneSequence,
      rightPaneSequence,
      leftPaneImage,
      rightPaneImage,
      leftPaneImageStyle,
      rightPaneImageStyle,
      onDragStart,
      onDragOver,
      onDragLeave,
      onDrop,
      handleMouseWheel,
      currentRegion,
      getCheckForRegion,
      getCheckStatusForRegion,
      getDiagnosisStatusForRegion,
      zoomedImage,
      zoomImage,
      isVideoFile,
      getSequenceTypeLabel,
      handleVideoError,
      leftVideoLoading,
      rightVideoLoading,
      handleVideoLoadStart,
      handleVideoLoaded,
      leftPaneDragOver,
      rightPaneDragOver,
      redirectCountdown,
    };
  },
});
</script> 