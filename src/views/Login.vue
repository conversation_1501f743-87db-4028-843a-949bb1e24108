<template>
  <div class="flex flex-col min-h-screen bg-gray-50">
    <!-- 导航栏 -->
    <div class="bg-white shadow-sm">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="flex justify-between h-16 items-center">
          <div class="flex items-center">
            <!-- <img src="/logo.png" alt="logo" class="w-8 h-8" /> -->
            <h5 class="ml-2 text-lg font-bold text-gray-700">
              医学影像诊断系统
            </h5>
          </div>
        </div>
      </div>
    </div>

    <!-- 主内容区 -->
    <div class="flex flex-1 justify-center items-center">
      <div class="w-full max-w-md px-8 py-6 bg-white rounded-lg shadow-md">
        <div class="text-center mb-8">
          <div class="text-2xl font-bold text-gray-700">用户登录</div>
        </div>

        <form @submit.prevent="handleLogin">
          <div class="mb-5">
            <label class="block mb-1.5 text-sm font-medium text-gray-700"
              >用户名</label
            >
            <input
              type="text"
              v-model="credentials.username"
              class="w-full px-3 py-3 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              placeholder="请输入用户名"
              required
            />
          </div>

          <div class="mb-5">
            <label class="block mb-1.5 text-sm font-medium text-gray-700"
              >密码</label
            >
            <input
              type="password"
              v-model="credentials.password"
              class="w-full px-3 py-3 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              placeholder="请输入密码"
              required
            />
          </div>

          <div class="flex items-center mb-5">
            <input
              type="checkbox"
              id="remember"
              v-model="credentials.remember"
              class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
            />
            <label for="remember" class="ml-2 text-sm text-gray-700"
              >记住我</label
            >
          </div>

          <div class="flex justify-center">
            <button
              type="submit"
              class="w-1/2 py-2.5 bg-blue-600 hover:bg-blue-700 text-white font-medium rounded-md focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
              :disabled="loading"
            >
              {{ loading ? "登录中..." : "登录" }}
            </button>
          </div>

          <div v-if="error" class="mt-4 text-red-500 text-center text-sm">
            {{ error }}
          </div>
        </form>
      </div>
    </div>

    <!-- 页脚 -->
    <div
      class="bg-white py-5 text-center text-gray-500 text-xs border-t border-gray-200"
    >
      © {{ new Date().getFullYear() }} 云南省毒品依赖戒治技术创新中心 版权所有
    </div>
  </div>
</template>

<script lang="ts">
import { defineComponent, reactive, ref, onMounted } from "vue";
import { useRouter } from "vue-router";
import { useAuthStore } from "@/store/auth";

export default defineComponent({
  name: "LoginView",
  setup() {
    const router = useRouter();
    const authStore = useAuthStore();
    const loading = ref(false);
    const error = ref("");

    const credentials = reactive({
      username: "",
      password: "",
      remember: false,
    });

    // Load saved credentials on component mount if they exist
    onMounted(() => {
      if (authStore.savedCredentials) {
        credentials.username = authStore.savedCredentials.username;
        credentials.password = authStore.savedCredentials.password;
        credentials.remember = true;
      }
    });

    async function handleLogin() {
      loading.value = true;
      error.value = "";

      try {
        const success = await authStore.login(credentials);
        if (success) {
          router.push("/dashboard");
        } else {
          error.value = "登录失败，请检查用户名和密码";
        }
      } catch (err) {
        error.value = "登录时发生错误，请稍后再试";
        console.error(err);
      } finally {
        loading.value = false;
      }
    }

    return {
      credentials,
      loading,
      error,
      handleLogin,
    };
  },
});
</script>

<style scoped>
.input:focus {
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.2);
}
</style> 