<template>
  <div class="min-h-screen flex flex-col bg-gray-50">
    <Navbar />

    <div class="flex flex-1">
      <!-- 侧边栏 -->
      <div
        class="w-auto min-w-[12rem] max-w-[16rem] bg-white border-r border-gray-200"
      >
        <div class="py-6 px-4">
          <div class="space-y-2">
            <!-- 使用检查部位组件 -->
            <CheckParts
              :current-region="currentRegion"
              :initial-menu-open="menuOpen.regions"
              :doctor-first-name="doctorFirstName"
              @select-region="selectRegion"
            />

            <!-- 使用检查报告组件 -->
            <CheckReports
              :current-report-type="currentReportType"
              :initial-menu-open="menuOpen.reports"
              @select-report-type="selectReportType"
            />

            <!-- 使用数据上传组件 -->
            <DataUpload
              :current-upload-type="currentUploadType"
              :initial-menu-open="menuOpen.upload"
              @select-upload-type="selectUploadType"
            />

            <!-- 使用系统管理组件 -->
            <SystemManagement
              :current-system-type="currentSystemType"
              :initial-menu-open="menuOpen.system"
              @select-system-type="selectSystemType"
            />
          </div>
        </div>
      </div>

      <!-- 主内容区 -->
      <div class="flex-1 p-8">
        <!-- 检查部位内容 -->
        <CheckRegionsContent
          v-if="currentView === 'regions'"
          :subjects="subjects"
          :loading="loading"
          :current-region="currentRegion"
          :date-range="dateRange"
          :doctor-first-name="doctorFirstName"
          :current-page="currentPage"
          :total-pages="totalPages"
          :total-subjects="totalItems"
          @filter-date="handleDateFilter"
          @edit-subject="navigateToSubject"
          @change-page="handlePageChange"
          @sort-direction-change="handleSortDirectionChange"
        />

        <!-- 检查报告内容 -->
        <CheckReportsContent
          v-else-if="currentView === 'reports'"
          :reports="reports"
          :loading="loading"
          :current-report-type="currentReportType"
          @change-type="selectReportType"
          @edit-report="handleEditReport"
        />

        <!-- 系统管理内容 -->
        <SystemManagementContent
          v-else-if="currentView === 'system'"
          :users="users"
          :logs="logs"
          :loading="loading"
          :current-system-type="currentSystemType"
          :current-page="currentPage"
          :total-pages="totalPages"
          :total-items="totalItems"
          :items-per-page="itemsPerPage"
          @change-type="selectSystemType"
          @edit-user="handleEditUser"
          @toggle-user-status="handleToggleUserStatus"
          @change-page="handlePageChange"
        />

        <!-- 数据上传内容 -->
        <DataUploadContent
          v-else
          :loading="loading"
          :current-upload-type="currentUploadType"
          @upload-excel="handleExcelUpload"
          @upload-images="handleImageUpload"
        />
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import { defineComponent, onMounted, computed, ref, watch } from "vue";
import { useRouter, useRoute } from "vue-router";
import { useDiagnosisStore } from "@/store/diagnosis";
import { useAuthStore } from "@/store/auth";
import Navbar from "@/components/common/Navbar.vue";
import { SubjectsFilter, fetchSubjects, Subject } from "@/services/diagnosis";
import CheckParts from "@/components/sidebar/CheckParts.vue";
import CheckReports from "@/components/sidebar/CheckReports.vue";
import SystemManagement from "@/components/sidebar/SystemManagement.vue";
import DataUpload from "@/components/sidebar/DataUpload.vue";
import CheckRegionsContent from "@/components/content/CheckRegionsContent.vue";
import CheckReportsContent from "@/components/content/CheckReportsContent.vue";
import SystemManagementContent from "@/components/content/SystemManagementContent.vue";
import DataUploadContent from "@/components/content/DataUploadContent.vue";
import type { User, SystemLog } from "@/types/system";
import type { Report } from "@/types/diagnosis";
import {
  fetchUsers,
  updateUserStatus,
  getDateRangeSettings,
  saveDateRangeSettings,
} from "@/services/system";

export default defineComponent({
  name: "DashboardView",

  components: {
    Navbar,
    CheckParts,
    CheckReports,
    SystemManagement,
    DataUpload,
    CheckRegionsContent,
    CheckReportsContent,
    SystemManagementContent,
    DataUploadContent,
  },

  setup() {
    const router = useRouter();
    const route = useRoute();
    const diagnosisStore = useDiagnosisStore();
    const authStore = useAuthStore();
    const currentView = ref<"regions" | "reports" | "system" | "upload">(
      "regions"
    );
    const currentRegion = ref<
      "head" | "abdomen" |  "others"
    >("head");
    const currentReportType = ref<"pending" | "completed">("pending");
    const currentSystemType = ref<"users" | "logs">("users");
    const currentUploadType = ref<"excel" | "images">("excel");
    const currentPage = ref(1);
    const itemsPerPage = ref(100);
    const totalPages = ref(1);
    const totalItems = ref(0);

    // 菜单展开状态
    const menuOpen = ref({
      regions: true,
      system: true,
      reports: true,
      upload: true,
    });

    // 数据状态
    const subjects = ref<Subject[]>([]);
    const reports = ref<Report[]>([]);
    const users = ref<User[]>([]);
    const logs = ref<SystemLog[]>([]);
    const loading = ref(false);

    // 日期范围状态
    const dateRange = ref({
      startDate: "",
      endDate: "",
    });

    // 获取用户姓名首字
    const doctorFirstName = computed(() => {
      const userName = authStore.user?.name || "";
      return userName.length > 0 ? userName.charAt(0) : "";
    });

    // 从本地存储恢复状态
    function loadStateFromStorage() {
      try {
        const savedState = localStorage.getItem("dashboardState");
        if (savedState) {
          const state = JSON.parse(savedState);
          currentView.value = state.view || "regions";
          currentRegion.value = state.region || "head";
          currentReportType.value = state.reportType || "pending";
          currentSystemType.value = state.systemType || "users";
          currentUploadType.value = state.uploadType || "excel";
          currentPage.value = state.page || 1;

          // 更新菜单打开状态
          if (state.menuOpen) {
            menuOpen.value = {
              ...menuOpen.value,
              ...state.menuOpen,
            };
          }
        }
      } catch (err) {
        console.error("加载状态失败", err);
      }
    }

    // 保存状态到本地存储
    function saveStateToStorage() {
      try {
        const state = {
          view: currentView.value,
          region: currentRegion.value,
          reportType: currentReportType.value,
          systemType: currentSystemType.value,
          uploadType: currentUploadType.value,
          page: currentPage.value,
          menuOpen: menuOpen.value,
        };
        localStorage.setItem("dashboardState", JSON.stringify(state));
      } catch (err) {
        console.error("保存状态失败", err);
      }
    }

    // 从URL查询参数中恢复状态
    function loadStateFromURL() {
      const region = route.query.region as string;
      const page = route.query.page
        ? parseInt(route.query.page as string)
        : null;
      const view = route.query.view as string;

      if (
        region &&
        ["head", "abdomen", "cspine", "lspine", "others"].includes(region)
      ) {
        currentRegion.value = region as any;
        currentView.value = "regions";
      }

      if (page && !isNaN(page) && page > 0) {
        currentPage.value = page;
      }

      if (view && ["regions", "reports", "system", "upload"].includes(view)) {
        currentView.value = view as any;
      }
    }

    // 更新URL状态
    function updateURLState() {
      const query: Record<string, string> = {};

      if (currentView.value === "regions") {
        query.region = currentRegion.value;
      } else {
        query.view = currentView.value;
      }

      if (currentPage.value > 1) {
        query.page = currentPage.value.toString();
      }

      router.replace({ query });
    }

    // 加载日期范围设置
    async function loadDateRangeSettings() {
      try {
        const dateSettings = await getDateRangeSettings();
        if (dateSettings && dateSettings.start_date && dateSettings.end_date) {
          dateRange.value = {
            startDate: dateSettings.start_date,
            endDate: dateSettings.end_date,
          };
          console.log("加载日期范围设置成功:", dateRange.value);
        }
      } catch (err) {
        console.error("加载日期范围设置失败", err);
      }
    }

    onMounted(() => {
      loadStateFromURL(); // 首先从URL加载状态
      loadStateFromStorage(); // 然后尝试从localStorage加载更完整的状态

      // 检查是否有从sessionStorage保存的返回位置信息
      try {
        const positionData = sessionStorage.getItem("diagnosisReturnPosition");
        if (positionData) {
          const returnPosition = JSON.parse(positionData);

          // 验证数据是否有效（检查时间戳不超过2小时）
          const now = new Date().getTime();
          const isValid =
            returnPosition &&
            returnPosition.timestamp &&
            now - returnPosition.timestamp < 2 * 60 * 60 * 1000;

          if (isValid) {
            // 恢复各种状态
            if (returnPosition.view) {
              currentView.value = returnPosition.view;
            }

            if (returnPosition.region) {
              currentRegion.value = returnPosition.region;
            }

            if (returnPosition.page && returnPosition.page > 0) {
              currentPage.value = returnPosition.page;
            }

            // 设置一个标志，表示这是从诊断页面返回的
            sessionStorage.setItem("isReturningFromDiagnosis", "true");
          }
        }
      } catch (err) {
        console.error("恢复位置信息失败:", err);
      }

      loadDateRangeSettings().then(() => {
        loadData(); // 先加载日期范围，再加载数据

        // 如果是从诊断页面返回，在页面加载后恢复滚动位置
        const attemptToRestoreScrollPosition = () => {
          try {
            const positionData = sessionStorage.getItem(
              "diagnosisReturnPosition"
            );
            const isReturning = sessionStorage.getItem(
              "isReturningFromDiagnosis"
            );

            if (positionData && isReturning) {
              const returnPosition = JSON.parse(positionData);
              if (
                returnPosition.scrollPosition &&
                returnPosition.scrollPosition > 0
              ) {
                // 保存目标滚动位置
                const targetScrollPosition = returnPosition.scrollPosition;

                // 执行滚动
                window.scrollTo({
                  top: targetScrollPosition,
                });
              } else {
                sessionStorage.removeItem("isReturningFromDiagnosis");
              }
            }
          } catch (err) {
            console.error("恢复滚动位置失败:", err);
            sessionStorage.removeItem("isReturningFromDiagnosis");
          }
        };

        // 多次尝试恢复滚动位置，以确保DOM完全加载
        setTimeout(attemptToRestoreScrollPosition, 200);
        setTimeout(attemptToRestoreScrollPosition, 500);
        setTimeout(attemptToRestoreScrollPosition, 1000);
      });
    });

    // 当视图或区域或页码变化时，保存状态并更新URL
    watch(
      [
        () => currentView.value,
        () => currentRegion.value,
        () => currentPage.value,
        () => currentReportType.value,
        () => currentSystemType.value,
        () => currentUploadType.value,
      ],
      () => {
        saveStateToStorage();
        updateURLState();
      }
    );

    function loadData() {
      loading.value = true;

      if (currentView.value === "regions") {
        loadSubjects();
      } else if (currentView.value === "reports") {
        loadReports();
      } else if (currentView.value === "system") {
        loadSystemData();
      } else {
        // 上传页面不需要预加载数据
        loading.value = false;
      }
    }

    async function loadSubjects() {
      try {
        const filter: SubjectsFilter = {
          region: currentRegion.value,
          start_date: dateRange.value.startDate,
          end_date: dateRange.value.endDate,
        };

        const response = await fetchSubjects(filter);

        // 处理返回的数据
        if (Array.isArray(response)) {
          // 如果直接返回数组，保存所有数据
          subjects.value = response;
          totalItems.value = response.length;
        } else if (response && response.data) {
          // 如果返回的是包含data和total的对象
          subjects.value = response.data;
          totalItems.value = response.total || response.data.length;
        } else {
          subjects.value = [];
          totalItems.value = 0;
        }

        // 计算总页数
        totalPages.value = Math.ceil(totalItems.value / itemsPerPage.value);
      } catch (err) {
        console.error("加载患者数据失败", err);
        subjects.value = [];
        totalItems.value = 0;
        totalPages.value = 1;
      } finally {
        loading.value = false;
      }
    }

    async function loadReports() {
      try {
        // 实现加载报告数据的逻辑
        reports.value = [];
      } catch (err) {
        console.error("加载报告数据失败", err);
      } finally {
        loading.value = false;
      }
    }

    async function loadSystemData() {
      try {
        if (currentSystemType.value === "users") {
          // 加载用户数据
          users.value = await fetchUsers();
        } else {
          // 实现加载日志数据的逻辑
          logs.value = [];
        }
      } catch (err) {
        console.error("加载系统数据失败", err);
      } finally {
        loading.value = false;
      }
    }

    function selectRegion(
      region: "head" | "abdomen" | "others"
    ) {
      currentRegion.value = region;
      currentView.value = "regions";
      currentPage.value = 1; // 切换区域时重置页码
      loadData();
    }

    function selectReportType(type: "pending" | "completed") {
      currentReportType.value = type;
      currentView.value = "reports";
      loadData();
    }

    function selectSystemType(type: "users" | "logs") {
      currentSystemType.value = type;
      currentView.value = "system";
      loadData();
    }

    function selectUploadType(type: "excel" | "images") {
      currentUploadType.value = type;
      currentView.value = "upload";
      loadData();
    }

    async function handleDateFilter(dates: {
      startDate: string;
      endDate: string;
    }) {
      try {
        // 更新当前日期范围
        dateRange.value = dates;

        // 重置页码到第一页
        currentPage.value = 1;

        // 使用传入的日期范围作为过滤条件并加载第一页数据
        loading.value = true;
        await loadSubjects(); // 只加载新的subjects数据
      } catch (err) {
        console.error("日期过滤失败", err);
      } finally {
        loading.value = false;
      }
    }

    function handleEditReport(reportId: string) {
      // 实现编辑报告逻辑
      router.push(`/reports/${reportId}`);
    }

    function handleEditUser(userId: string) {
      // 实现编辑用户逻辑
      router.push(`/users/${userId}`);
    }

    async function handleToggleUserStatus(username: string) {
      try {
        loading.value = true;
        // 找到用户并获取当前状态
        const user = users.value.find((u) => u.username === username);
        if (user) {
          // 根据当前状态计算新状态
          const isCurrentlyActive = user.is_disabled === false || user.active;
          // 调用API更新用户状态
          await updateUserStatus(username, !isCurrentlyActive);
          // 重新加载用户数据
          await loadSystemData();
        }
      } catch (err) {
        console.error("更新用户状态失败", err);
      } finally {
        loading.value = false;
      }
    }

    function handlePageChange(page: number) {
      currentPage.value = page;
      // 前端分页不需要重新加载数据
      // loadData();
    }

    function navigateToSubject(subjectId: string, region: string) {
      // 将当前状态保存到localStorage，以便从诊断页面返回时恢复
      saveStateToStorage();

      // 保存当前状态到sessionStorage，这样会在会话期间保持，但不会长期保存
      // 使用sessionStorage保存当前查看位置的详细信息
      try {
        const positionInfo = {
          view: currentView.value,
          region: currentRegion.value,
          page: currentPage.value,
          sortDirection: localStorage.getItem("sortDirection") || "desc", // 获取从PatientTable传递的排序方向
          scrollPosition: window.scrollY, // 保存滚动位置
          timestamp: new Date().getTime(), // 添加时间戳以验证数据的有效性
        };
        sessionStorage.setItem(
          "diagnosisReturnPosition",
          JSON.stringify(positionInfo)
        );
      } catch (err) {
        console.error("保存位置信息失败:", err);
      }

      // 导航到诊断页面时携带区域参数和返回URL
      router.push({
        path: `/diagnosis/${subjectId}`,
        query: {
          region: currentRegion.value,
          returnTo: "dashboard",
          returnPage: currentPage.value.toString(), // 添加当前页码作为查询参数
        },
      });
    }

    async function handleExcelUpload(file: File) {
      try {
        loading.value = true;
        // 这里实现Excel上传逻辑
        console.log("上传Excel文件", file.name);
        // 示例:
        // const formData = new FormData();
        // formData.append('file', file);
        // await uploadExcelFile(formData);

        // 显示成功消息
        alert(`Excel文件 ${file.name} 上传成功`);
      } catch (err) {
        console.error("Excel上传失败", err);
        alert("Excel上传失败");
      } finally {
        loading.value = false;
      }
    }

    async function handleImageUpload(files: FileList) {
      try {
        loading.value = true;
        // 这里实现图片上传逻辑
        console.log(`上传 ${files.length} 个图片文件`);
        // 示例:
        // const formData = new FormData();
        // Array.from(files).forEach(file => {
        //   formData.append('images', file);
        // });
        // await uploadImages(formData);

        // 显示成功消息
        alert(`${files.length} 个图片文件上传成功`);
      } catch (err) {
        console.error("图片上传失败", err);
        alert("图片上传失败");
      } finally {
        loading.value = false;
      }
    }

    function handleSortDirectionChange(direction: "asc" | "desc") {
      // 保存排序方向到本地存储，以便从诊断页面返回时恢复
      try {
        localStorage.setItem("sortDirection", direction);
        console.log("排序方向已保存:", direction);
      } catch (err) {
        console.error("保存排序方向失败:", err);
      }
    }

    return {
      currentView,
      currentRegion,
      currentReportType,
      currentSystemType,
      currentUploadType,
      currentPage,
      totalPages,
      totalItems,
      itemsPerPage,
      menuOpen,
      subjects,
      reports,
      users,
      logs,
      loading,
      dateRange,
      doctorFirstName,
      selectRegion,
      selectReportType,
      selectSystemType,
      selectUploadType,
      handleDateFilter,
      handleEditReport,
      handleEditUser,
      handleToggleUserStatus,
      handlePageChange,
      navigateToSubject,
      handleExcelUpload,
      handleImageUpload,
      handleSortDirectionChange,
    };
  },
});
</script>