<template>
  <div class="bg-white shadow-sm border-b border-gray-200">
    <div class="max-w-full mx-auto px-4 sm:px-6 lg:px-8">
      <div class="flex justify-between h-16 items-center">
        <!-- 左侧 Logo 和标题 -->
        <div class="flex items-center">
          <img src="/logo.png" alt="logo" class="w-8 h-8" />
          <h5 class="ml-2 text-lg font-bold text-gray-700">医学影像诊断系统</h5>
        </div>

        <!-- 中间区域 -->
        <div class="flex-1"></div>

        <!-- 右侧用户信息和操作 -->
        <div class="flex items-center space-x-4">
          <!-- 通知按钮 -->
          <div class="relative">
            <button
              class="flex items-center justify-center w-8 h-8 rounded-full border border-gray-300 text-gray-600 hover:bg-gray-100"
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                class="h-5 w-5"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  stroke-width="2"
                  d="M15 17h5l-1.405-1.405A2.032 2.032 0 0118 14.158V11a6.002 6.002 0 00-4-5.659V5a2 2 0 10-4 0v.341C7.67 6.165 6 8.388 6 11v3.159c0 .538-.214 1.055-.595 1.436L4 17h5m6 0v1a3 3 0 11-6 0v-1m6 0H9"
                />
              </svg>
            </button>
            <!-- 通知标记 -->
            <span
              class="absolute top-0 right-0 block h-2 w-2 rounded-full bg-red-500"
            ></span>
          </div>

          <!-- 用户头像和下拉菜单 -->
          <div class="relative" ref="userDropdownRef">
            <div
              @click="toggleUserDropdown"
              class="flex items-center cursor-pointer px-3 py-2 rounded-lg hover:bg-gray-100 transition-colors duration-200"
            >
              <span class="text-gray-700 mr-2">{{ user?.name || "用户" }}</span>
              <div
                class="w-8 h-8 rounded-full bg-gray-100 flex items-center justify-center text-gray-700 relative"
              >
                <!-- 用户头像SVG图标 -->
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  class="h-5 w-5"
                  viewBox="0 0 20 20"
                  fill="currentColor"
                >
                  <path
                    fill-rule="evenodd"
                    d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z"
                    clip-rule="evenodd"
                  />
                </svg>

                <!-- 管理员标识 -->
                <span
                  v-if="isAdmin"
                  class="absolute -top-1 -right-1 bg-blue-500 rounded-full p-1"
                >
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    class="h-3 w-3 text-white"
                    viewBox="0 0 20 20"
                    fill="currentColor"
                  >
                    <path
                      d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118l-2.8-2.034c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"
                    />
                  </svg>
                </span>
              </div>
              <svg
                class="ml-1 h-4 w-4 text-gray-500"
                xmlns="http://www.w3.org/2000/svg"
                viewBox="0 0 20 20"
                fill="currentColor"
              >
                <path
                  fill-rule="evenodd"
                  d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z"
                  clip-rule="evenodd"
                />
              </svg>
            </div>

            <!-- 用户下拉菜单 -->
            <div
              v-if="isUserDropdownOpen"
              class="absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg py-1 z-10 border border-gray-200"
            >
              <div class="px-4 py-2 border-b border-gray-100">
                <p class="text-sm font-medium text-gray-900">
                  {{ user?.name || "用户" }}
                </p>
                <p class="text-xs text-gray-500">{{ user?.username || "" }}</p>
              </div>
              <a
                href="/dashboard?view=system"
                class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                >设置</a
              >
              <div class="border-t border-gray-100"></div>
              <button
                @click="logout"
                class="block w-full text-left px-4 py-2 text-sm text-red-600 hover:bg-gray-100"
              >
                退出登录
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import { defineComponent, computed, ref, onMounted, onUnmounted } from "vue";
import { useRouter } from "vue-router";
import { useAuthStore } from "@/store/auth";

export default defineComponent({
  name: "Navbar",
  setup() {
    const router = useRouter();
    const authStore = useAuthStore();
    const userDropdownRef = ref<HTMLElement | null>(null);
    const isUserDropdownOpen = ref(false);

    const user = computed(() => authStore.user);

    // 判断是否为管理员用户
    const isAdmin = computed(() => {
      return user.value?.username === "admin" || user.value?.role === "admin";
    });

    function toggleUserDropdown() {
      isUserDropdownOpen.value = !isUserDropdownOpen.value;
    }

    // 点击外部关闭下拉菜单
    function closeDropdownOnClickOutside(event: MouseEvent) {
      if (
        userDropdownRef.value &&
        !userDropdownRef.value.contains(event.target as Node)
      ) {
        isUserDropdownOpen.value = false;
      }
    }

    onMounted(() => {
      document.addEventListener("click", closeDropdownOnClickOutside);
    });

    onUnmounted(() => {
      document.removeEventListener("click", closeDropdownOnClickOutside);
    });

    function logout() {
      authStore.logout();
      router.push("/login");
      isUserDropdownOpen.value = false;
    }

    return {
      user,
      isAdmin,
      logout,
      userDropdownRef,
      isUserDropdownOpen,
      toggleUserDropdown,
    };
  },
});
</script>

<style scoped>
/* 下拉菜单动画 */
.dropdown-enter-active,
.dropdown-leave-active {
  transition: opacity 0.2s, transform 0.2s;
}

.dropdown-enter-from,
.dropdown-leave-to {
  opacity: 0;
  transform: translateY(-10px);
}
</style> 