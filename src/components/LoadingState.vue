<template>
  <div>
    <div v-if="loading" class="flex justify-center items-center p-4">
      <div class="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-blue-500"></div>
      <span class="ml-2 text-gray-600">加载中...</span>
    </div>
    
    <div v-if="error" class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative my-2" role="alert">
      <strong class="font-bold">错误!</strong>
      <span class="block sm:inline"> {{ error }}</span>
    </div>
  </div>
</template>

<script setup lang="ts">
defineProps<{
  loading: boolean;
  error: string;
}>();
</script> 