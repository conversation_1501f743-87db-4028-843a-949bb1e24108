<template>
  <div class="sidebar-section">
    <div
      class="sidebar-header flex justify-between items-center p-2 cursor-pointer"
      @click="toggleMenu"
    >
      <div class="flex items-center space-x-2">
        <svg
          xmlns="http://www.w3.org/2000/svg"
          class="h-5 w-5 text-blue-600"
          fill="none"
          viewBox="0 0 24 24"
          stroke="currentColor"
        >
          <path
            stroke-linecap="round"
            stroke-linejoin="round"
            stroke-width="2"
            d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"
          />
        </svg>
        <span class="font-medium text-gray-700">检查部位</span>
      </div>
      <svg
        xmlns="http://www.w3.org/2000/svg"
        class="h-4 w-4 text-gray-500 transition-transform duration-200"
        :class="{ 'transform rotate-180': menuOpen }"
        fill="none"
        viewBox="0 0 24 24"
        stroke="currentColor"
      >
        <path
          stroke-linecap="round"
          stroke-linejoin="round"
          stroke-width="2"
          d="M19 9l-7 7-7-7"
        />
      </svg>
    </div>

    <div
      v-if="menuOpen"
      class="sidebar-content pl-4 py-2 space-y-1 border-l border-gray-200 ml-3"
    >
      <div
        v-for="region in regions"
        :key="region.id"
        @click="selectRegion(region.id)"
        class="sidebar-item flex items-center space-x-2 px-2 py-1.5 rounded hover:bg-gray-100 cursor-pointer"
        :class="{ 'bg-blue-50 text-blue-700': currentRegion === region.id }"
      >
        <svg
          xmlns="http://www.w3.org/2000/svg"
          class="h-4 w-4"
          :class="{
            'text-blue-600': currentRegion === region.id,
            'text-gray-500': currentRegion !== region.id,
          }"
          fill="none"
          viewBox="0 0 24 24"
          stroke="currentColor"
          v-html="region.icon"
        ></svg>
        <span
          class="text-sm"
          :class="{ 'font-medium': currentRegion === region.id }"
          >{{ region.name }}</span
        >
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import { defineComponent, ref, PropType, computed } from "vue";

export interface Region {
  id: string;
  name: string;
  icon: string;
  count: number;
}

export default defineComponent({
  name: "CheckParts",
  props: {
    currentRegion: {
      type: String as PropType<string>,
      required: true,
    },
    initialMenuOpen: {
      type: Boolean,
      default: true,
    },
    doctorFirstName: {
      type: String,
      default: "",
    },
  },
  emits: ["select-region"],
  setup(props, { emit }) {
    const menuOpen = ref(props.initialMenuOpen);

    const regions = computed(() => [
      {
        id: "head",
        name: "头颅",
        icon: '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197M13 7a4 4 0 11-8 0 4 4 0 018 0z"/>',
      },
      {
        id: "abdomen",
        name: "腹部",
        icon: '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z M6 9h12M9 6v6m6-6v6"/>',
      },
      {
        id: "others",
        name: "其他",
        icon: '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-3 7h3m-3 4h3m-6-4h.01M9 16h.01"/>',
      },
    ]);

    function toggleMenu() {
      menuOpen.value = !menuOpen.value;
    }

    function selectRegion(regionId: string) {
      emit("select-region", regionId);
    }

    return {
      menuOpen,
      regions,
      toggleMenu,
      selectRegion,
    };
  },
});
</script> 