<template>
  <div class="sidebar-section">
    <div
      class="sidebar-header flex justify-between items-center p-2 cursor-pointer"
      @click="toggleMenu"
    >
      <div class="flex items-center space-x-2">
        <svg
          xmlns="http://www.w3.org/2000/svg"
          class="h-5 w-5 text-blue-600"
          fill="none"
          viewBox="0 0 24 24"
          stroke="currentColor"
        >
          <path
            stroke-linecap="round"
            stroke-linejoin="round"
            stroke-width="2"
            d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"
          />
          <path
            stroke-linecap="round"
            stroke-linejoin="round"
            stroke-width="2"
            d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"
          />
        </svg>
        <span class="font-medium text-gray-700">系统管理</span>
      </div>
      <svg
        xmlns="http://www.w3.org/2000/svg"
        class="h-4 w-4 text-gray-500 transition-transform duration-200"
        :class="{ 'transform rotate-180': menuOpen }"
        fill="none"
        viewBox="0 0 24 24"
        stroke="currentColor"
      >
        <path
          stroke-linecap="round"
          stroke-linejoin="round"
          stroke-width="2"
          d="M19 9l-7 7-7-7"
        />
      </svg>
    </div>

    <div
      v-if="menuOpen"
      class="sidebar-content pl-4 py-2 space-y-1 border-l border-gray-200 ml-3"
    >
      <div
        @click="selectSystemType('users')"
        class="sidebar-item flex items-center space-x-2 px-2 py-1.5 rounded hover:bg-gray-100 cursor-pointer"
        :class="{ 'bg-blue-50 text-blue-700': currentSystemType === 'users' }"
      >
        <svg
          xmlns="http://www.w3.org/2000/svg"
          class="h-4 w-4"
          :class="{
            'text-blue-600': currentSystemType === 'users',
            'text-gray-500': currentSystemType !== 'users',
          }"
          fill="none"
          viewBox="0 0 24 24"
          stroke="currentColor"
        >
          <path
            stroke-linecap="round"
            stroke-linejoin="round"
            stroke-width="2"
            d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197M13 7a4 4 0 11-8 0 4 4 0 018 0z"
          />
        </svg>
        <span
          class="text-sm"
          :class="{ 'font-medium': currentSystemType === 'users' }"
          >系统配置</span
        >
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import { defineComponent, ref, PropType } from "vue";

export default defineComponent({
  name: "SystemManagement",
  props: {
    currentSystemType: {
      type: String as PropType<"users">,
      required: true,
    },
    initialMenuOpen: {
      type: Boolean,
      default: true,
    },
  },
  emits: ["select-system-type"],
  setup(props, { emit }) {
    const menuOpen = ref(props.initialMenuOpen);

    function toggleMenu() {
      menuOpen.value = !menuOpen.value;
    }

    function selectSystemType(type: "users") {
      emit("select-system-type", type);
    }

    return {
      menuOpen,
      toggleMenu,
      selectSystemType,
    };
  },
});
</script> 