<template>
  <div class="sidebar-section">
    <div
      class="sidebar-header flex justify-between items-center p-2 cursor-pointer"
      @click="toggleMenu"
    >
      <div class="flex items-center space-x-2">
        <svg
          xmlns="http://www.w3.org/2000/svg"
          class="h-5 w-5 text-blue-600"
          fill="none"
          viewBox="0 0 24 24"
          stroke="currentColor"
        >
          <path
            stroke-linecap="round"
            stroke-linejoin="round"
            stroke-width="2"
            d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"
          />
        </svg>
        <span class="font-medium text-gray-700">数据上传</span>
      </div>
      <svg
        xmlns="http://www.w3.org/2000/svg"
        class="h-4 w-4 text-gray-500 transition-transform duration-200"
        :class="{ 'transform rotate-180': isMenuOpen }"
        fill="none"
        viewBox="0 0 24 24"
        stroke="currentColor"
      >
        <path
          stroke-linecap="round"
          stroke-linejoin="round"
          stroke-width="2"
          d="M19 9l-7 7-7-7"
        />
      </svg>
    </div>

    <div
      v-if="isMenuOpen"
      class="sidebar-content pl-4 py-2 space-y-1 border-l border-gray-200 ml-3"
    >
      <!-- Excel上传选项 -->
      <div
        class="sidebar-item flex items-center space-x-2 px-2 py-1.5 rounded hover:bg-gray-100 cursor-pointer"
        :class="{ 'bg-blue-50 text-blue-700': currentUploadType === 'subjects_info' }"
        @click="selectUploadType('subjects_info')"
      >
        <svg
          xmlns="http://www.w3.org/2000/svg"
          class="h-4 w-4"
          :class="{
            'text-blue-600': currentUploadType === 'subjects_info',
            'text-gray-500': currentUploadType !== 'subjects_info',
          }"
          fill="none"
          viewBox="0 0 24 24"
          stroke="currentColor"
        >
          <path
            stroke-linecap="round"
            stroke-linejoin="round"
            stroke-width="2"
            d="M9 17v-2m3 2v-4m3 4v-6m2 10H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
          />
        </svg>
        <span
          class="text-sm"
          :class="{ 'font-medium': currentUploadType === 'subjects_info' }"
          >检查信息上传</span
        >
      </div>

      <!-- 图像上传选项 -->
      <div
        class="sidebar-item flex items-center space-x-2 px-2 py-1.5 rounded hover:bg-gray-100 cursor-pointer"
        :class="{ 'bg-blue-50 text-blue-700': currentUploadType === 'images' }"
        @click="selectUploadType('images')"
      >
        <svg
          xmlns="http://www.w3.org/2000/svg"
          class="h-4 w-4"
          :class="{
            'text-blue-600': currentUploadType === 'images',
            'text-gray-500': currentUploadType !== 'images',
          }"
          fill="none"
          viewBox="0 0 24 24"
          stroke="currentColor"
        >
          <path
            stroke-linecap="round"
            stroke-linejoin="round"
            stroke-width="2"
            d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"
          />
        </svg>
        <span
          class="text-sm"
          :class="{ 'font-medium': currentUploadType === 'images' }"
          >图像上传</span
        >
      </div>

      <!-- 心脏报告上传选项 -->
      <div
        class="sidebar-item flex items-center space-x-2 px-2 py-1.5 rounded hover:bg-gray-100 cursor-pointer"
        :class="{
          'bg-blue-50 text-blue-700': currentUploadType === 'heart_report',
        }"
        @click="selectUploadType('heart_report')"
      >
        <svg
          xmlns="http://www.w3.org/2000/svg"
          class="h-4 w-4"
          :class="{
            'text-blue-600': currentUploadType === 'heart_report',
            'text-gray-500': currentUploadType !== 'heart_report',
          }"
          fill="none"
          viewBox="0 0 24 24"
          stroke="currentColor"
        >
          <path
            stroke-linecap="round"
            stroke-linejoin="round"
            stroke-width="2"
            d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"
          />
        </svg>
        <span
          class="text-sm"
          :class="{ 'font-medium': currentUploadType === 'heart_report' }"
          >心脏报告上传</span
        >
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import { defineComponent, ref, onMounted } from "vue";

export default defineComponent({
  name: "DataUpload",

  props: {
    currentUploadType: {
      type: String as () => "subjects_info" | "images" | "heart_report",
      required: true,
    },
    initialMenuOpen: {
      type: Boolean,
      default: false,
    },
  },

  emits: ["select-upload-type"],

  setup(props, { emit }) {
    const isMenuOpen = ref(props.initialMenuOpen);

    function toggleMenu() {
      isMenuOpen.value = !isMenuOpen.value;
    }

    function selectUploadType(type: "subjects_info" | "images" | "heart_report") {
      emit("select-upload-type", type);
    }

    onMounted(() => {
      // 如果当前选中的是此模块的某个类型，确保菜单展开
      if (
        props.currentUploadType === "subjects_info" ||
        props.currentUploadType === "images" ||
        props.currentUploadType === "heart_report"
      ) {
        isMenuOpen.value = true;
      }
    });

    return {
      isMenuOpen,
      toggleMenu,
      selectUploadType,
    };
  },
});
</script> 