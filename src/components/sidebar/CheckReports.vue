<template>
  <div class="sidebar-section">
    <div
      class="sidebar-header flex justify-between items-center p-2 cursor-pointer"
      @click="toggleMenu"
    >
      <div class="flex items-center space-x-2">
        <svg
          xmlns="http://www.w3.org/2000/svg"
          class="h-5 w-5 text-blue-600"
          fill="none"
          viewBox="0 0 24 24"
          stroke="currentColor"
        >
          <path
            stroke-linecap="round"
            stroke-linejoin="round"
            stroke-width="2"
            d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
          />
        </svg>
        <span class="font-medium text-gray-700">报告管理</span>
      </div>
      <svg
        xmlns="http://www.w3.org/2000/svg"
        class="h-4 w-4 text-gray-500 transition-transform duration-200"
        :class="{ 'transform rotate-180': menuOpen }"
        fill="none"
        viewBox="0 0 24 24"
        stroke="currentColor"
      >
        <path
          stroke-linecap="round"
          stroke-linejoin="round"
          stroke-width="2"
          d="M19 9l-7 7-7-7"
        />
      </svg>
    </div>

    <div
      v-if="menuOpen"
      class="sidebar-content pl-4 py-2 space-y-1 border-l border-gray-200 ml-3"
    >
      <div
        @click="selectReportType('completed')"
        class="sidebar-item flex items-center space-x-2 px-2 py-1.5 rounded hover:bg-gray-100 cursor-pointer"
        :class="{
          'bg-blue-50 text-blue-700': currentReportType === 'completed',
        }"
      >
        <svg
          xmlns="http://www.w3.org/2000/svg"
          class="h-4 w-4"
          :class="{
            'text-blue-600': currentReportType === 'completed',
            'text-gray-500': currentReportType !== 'completed',
          }"
          fill="none"
          viewBox="0 0 24 24"
          stroke="currentColor"
        >
          <path
            stroke-linecap="round"
            stroke-linejoin="round"
            stroke-width="2"
            d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"
          />
        </svg>
        <span
          class="text-sm"
          :class="{ 'font-medium': currentReportType === 'completed' }"
          >报告下载</span
        >
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import { defineComponent, ref, PropType } from "vue";

export default defineComponent({
  name: "CheckReports",
  props: {
    currentReportType: {
      type: String as PropType<"pending" | "completed">,
      required: true,
    },
    initialMenuOpen: {
      type: Boolean,
      default: true,
    },
  },
  emits: ["select-report-type"],
  setup(props, { emit }) {
    const menuOpen = ref(props.initialMenuOpen);

    function toggleMenu() {
      menuOpen.value = !menuOpen.value;
    }

    function selectReportType(type: "pending" | "completed") {
      emit("select-report-type", type);
    }

    return {
      menuOpen,
      toggleMenu,
      selectReportType,
    };
  },
});
</script> 