<template>
  <div class="overflow-x-auto">
    <table class="min-w-full divide-y divide-gray-200 table-fixed">
      <thead class="bg-gray-50">
        <tr>
          <th
            scope="col"
            class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-10"
          >
            序号
          </th>
          <th
            scope="col"
            class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-20 cursor-pointer"
            @click="toggleSortDirection"
          >
            日期
            <span v-if="sortDirection === 'desc'" class="ml-1">↓</span>
            <span v-else-if="sortDirection === 'asc'" class="ml-1">↑</span>
          </th>
          <th
            scope="col"
            class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-20"
          >
            姓名
          </th>
          <th
            scope="col"
            class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-28"
          >
            诊断老师
          </th>
          <th
            scope="col"
            class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-24"
          >
            诊断部位
          </th>
          <th
            scope="col"
            class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-24"
          >
            序列数量
          </th>
          <th
            scope="col"
            class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-24"
          >
            诊断状态
          </th>
          <th
            scope="col"
            class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-24"
          >
            备注
          </th>
          <th
            scope="col"
            class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-20"
          >
            操作
          </th>
        </tr>
      </thead>
      <tbody class="bg-white divide-y divide-gray-200">
        <template v-if="loading">
          <tr v-for="i in 5" :key="`skeleton-${i}`" class="animate-pulse">
            <td class="px-6 py-4">
              <div class="h-3 bg-gray-200 rounded w-6 skeleton-gradient"></div>
            </td>
            <td class="px-6 py-4">
              <div class="h-3 bg-gray-200 rounded w-24 skeleton-gradient"></div>
            </td>
            <td class="px-6 py-4">
              <div class="h-3 bg-gray-200 rounded w-20 skeleton-gradient"></div>
            </td>
            <td class="px-6 py-4">
              <div class="h-3 bg-gray-200 rounded w-28 skeleton-gradient"></div>
            </td>
            <td class="px-6 py-4">
              <div class="h-3 bg-gray-200 rounded w-24 skeleton-gradient"></div>
            </td>
            <td class="px-6 py-4 text-center">
              <div
                class="h-6 bg-gray-200 rounded-full w-8 mx-auto skeleton-gradient"
              ></div>
            </td>
            <td class="px-6 py-4">
              <div
                class="h-6 bg-gray-200 rounded-full w-16 mx-auto skeleton-gradient"
              ></div>
            </td>
            <td class="px-6 py-4">
              <div class="h-3 bg-gray-200 rounded w-32 skeleton-gradient"></div>
            </td>
            <td class="px-6 py-4">
              <div class="h-8 bg-gray-200 rounded w-16 skeleton-gradient"></div>
            </td>
          </tr>
        </template>

        <template v-else>
          <tr
            v-for="(subject, index) in paginatedSubjects"
            :key="subject.subject_id"
          >
            <td class="px-6 py-4 text-sm text-gray-500">
              {{ getRowNumber(index) }}
            </td>
            <td class="px-6 py-4 text-sm text-gray-500">
              {{ formatDate(subject.check_date) }}
            </td>
            <td class="px-6 py-4 text-sm font-medium text-gray-900">
              {{ subject.pinyin }}
            </td>
            <td class="px-6 py-4 text-sm text-gray-500">
              <template v-if="isLoadingSubmitUsers[subject.subject_id]">
                <div class="animate-pulse">加载中...</div>
              </template>
              <template v-else>
                <span
                  :class="[
                    submitUsers[subject.subject_id]
                      ? 'px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800'
                      : '',
                  ]"
                >
                  {{ submitUsers[subject.subject_id] || "未诊断" }}
                </span>
              </template>
            </td>
            <td class="px-6 py-4 text-sm text-gray-500">
              {{ subject.region || props.region || "未知" }}
            </td>
            <td class="px-6 py-4 text-center">
              <span
                v-if="sequenceCounts[subject.subject_id] !== undefined"
                :class="[
                  'px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full',
                  sequenceCounts[subject.subject_id] > 0
                    ? 'bg-blue-100 text-blue-800'
                    : 'bg-gray-100 text-gray-800',
                ]"
              >
                {{ sequenceCounts[subject.subject_id] }}
              </span>
              <span
                v-else
                :class="[
                  'px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full',
                  isLoadingSequences[subject.subject_id]
                    ? 'bg-yellow-100 text-yellow-800 animate-pulse'
                    : 'bg-gray-100 text-gray-800',
                ]"
              >
                {{ isLoadingSequences[subject.subject_id] ? "加载中..." : "0" }}
              </span>
            </td>
            <td class="px-6 py-4">
              <span
                :class="[
                  'px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full',
                  getDiagnosisStatusClass(subject.diagnosis_status),
                ]"
              >
                {{ getDiagnosisStatusText(subject.diagnosis_status) }}
              </span>
            </td>
            <td class="px-6 py-4 text-sm text-gray-500">
              <div
                class="max-w-xs overflow-hidden"
                :title="subject.remark || '无'"
              >
                <div class="line-clamp-2 break-words">
                  {{ subject.remark || "无" }}
                </div>
              </div>
            </td>
            <td class="px-6 py-4 text-sm font-medium">
              <button
                @click="
                  $emit('edit-subject', subject.subject_id, subject.region)
                "
                class="text-blue-600 hover:text-blue-900 bg-blue-50 hover:bg-blue-100 px-3 py-1 rounded-md mr-2"
              >
                编辑
              </button>
            </td>
          </tr>

          <!-- 如果没有数据，显示空状态 -->
          <tr v-if="paginatedSubjects.length === 0">
            <td colspan="8" class="px-6 py-10 text-center text-gray-500">
              暂无患者数据
            </td>
          </tr>
        </template>
      </tbody>
    </table>

    <!-- 分页 -->
    <div
      class="px-6 py-4 flex items-center justify-between border-t border-gray-200"
    >
      <div class="text-sm text-gray-500">
        <template v-if="loading">
          <div
            class="h-4 bg-gray-200 rounded w-48 animate-pulse skeleton-gradient"
          ></div>
        </template>
        <template v-else>
          显示数据
          {{ paginatedSubjects.length > 0 ? currentPageStartIndex : 0 }} 至
          {{ currentPageEndIndex }} 共 {{ totalSubjects }} 条记录
        </template>
      </div>
      <div class="flex space-x-2">
        <template v-if="loading">
          <div class="flex space-x-2">
            <div
              v-for="i in 3"
              :key="`skeleton-page-${i}`"
              class="h-8 bg-gray-200 rounded w-8 animate-pulse skeleton-gradient"
            ></div>
          </div>
        </template>
        <template v-else>
          <button
            @click="$emit('change-page', currentPage - 1)"
            :disabled="currentPage === 1"
            :class="[
              'px-3 py-1 border border-gray-300 rounded-md text-sm',
              currentPage === 1
                ? 'opacity-50 cursor-not-allowed'
                : 'hover:bg-gray-50',
            ]"
          >
            &lt;
          </button>
          <button
            v-for="page in displayedPages"
            :key="page"
            @click="$emit('change-page', page)"
            :class="[
              'px-3 py-1 border border-gray-300 rounded-md text-sm',
              currentPage === page
                ? 'bg-blue-600 text-white'
                : 'hover:bg-gray-50',
            ]"
          >
            {{ page }}
          </button>
          <button
            @click="$emit('change-page', currentPage + 1)"
            :disabled="currentPage === totalPages"
            :class="[
              'px-3 py-1 border border-gray-300 rounded-md text-sm',
              currentPage === totalPages
                ? 'opacity-50 cursor-not-allowed'
                : 'hover:bg-gray-50',
            ]"
          >
            &gt;
          </button>
        </template>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import { defineComponent, computed, ref, watch, onMounted } from "vue";
import {
  fetchSequencesCounts,
  fetchSubmitUsers,
} from "../../services/diagnosis";

// 定义本地Subject接口，避免依赖外部类型文件
interface Subject {
  subject_id: string;
  name: string;
  check_date: string;
  remark: string;
  diagnosis_status?: string; // 从后端获取的诊断状态: pending, draft, submitted
  submit_user?: string; // 诊断提交者
  region?: string; // 诊断部位
}

export default defineComponent({
  name: "PatientTable",

  props: {
    subjects: {
      type: Array as () => Subject[],
      required: true,
    },
    loading: {
      type: Boolean,
      default: false,
    },
    currentPage: {
      type: Number,
      default: 1,
    },
    totalPages: {
      type: Number,
      default: 1,
    },
    totalSubjects: {
      type: Number,
      required: true,
    },
    itemsPerPage: {
      type: Number,
      default: 100,
    },
    region: {
      type: String,
      default: "head", // 默认区域
    },
  },

  emits: ["edit-subject", "change-page", "sort-direction-change"],

  setup(props, { emit }) {
    // 存储每个受试者的序列数量
    const sequenceCounts = ref<Record<string, number>>({});
    const isLoadingSequences = ref<Record<string, boolean>>({});
    // 存储每个受试者的诊断老师信息
    const submitUsers = ref<Record<string, string>>({});
    const isLoadingSubmitUsers = ref<Record<string, boolean>>({});
    // 排序方向：desc（从新到旧）或 asc（从旧到新）
    const sortDirection = ref<"desc" | "asc">("desc");

    // 切换排序方向
    const toggleSortDirection = () => {
      sortDirection.value = sortDirection.value === "desc" ? "asc" : "desc";
      emit("sort-direction-change", sortDirection.value);

      // 将排序方向保存到localStorage，以便返回时能恢复相同排序
      try {
        localStorage.setItem("sortDirection", sortDirection.value);
        localStorage.setItem(
          "sortDirectionChangedAt",
          new Date().getTime().toString()
        );

        // 如果正在变更排序方向，可能导致表格变化，需要更新滚动位置
        setTimeout(() => {
          // 检查是否有已保存的返回位置信息
          const positionData = sessionStorage.getItem(
            "diagnosisReturnPosition"
          );
          if (positionData) {
            try {
              const positionInfo = JSON.parse(positionData);
              // 更新其中的排序方向和时间戳
              positionInfo.sortDirection = sortDirection.value;
              positionInfo.timestamp = new Date().getTime();

              // 如果需要，也可以更新滚动位置
              const currentScroll =
                window.scrollY ||
                document.documentElement.scrollTop ||
                document.body.scrollTop;
              if (currentScroll > 50) {
                positionInfo.scrollPosition = currentScroll;
              }

              // 保存回sessionStorage
              sessionStorage.setItem(
                "diagnosisReturnPosition",
                JSON.stringify(positionInfo)
              );
              console.log("在排序变更后更新位置信息:", positionInfo);
            } catch (e) {
              console.error("更新位置信息失败:", e);
            }
          }
        }, 10);
      } catch (error) {
        console.error("保存排序方向失败:", error);
      }
    };

    // 从本地存储加载序列数据
    const loadSequenceCountsFromLocalStorage = () => {
      try {
        const cacheKey = `sequenceCounts_${props.region}`;
        const storedData = localStorage.getItem(cacheKey);
        if (storedData) {
          const parsedData = JSON.parse(storedData);
          // 检查缓存是否过期（24小时）
          const now = new Date().getTime();
          if (
            parsedData.timestamp &&
            now - parsedData.timestamp < 24 * 60 * 60 * 1000
          ) {
            sequenceCounts.value = parsedData.counts || {};
          }
        }
      } catch (error) {
        console.error("Error loading cached sequence counts:", error);
      }
    };

    // 从本地存储加载诊断老师信息
    const loadSubmitUsersFromLocalStorage = () => {
      try {
        const cacheKey = `submitUsers_${props.region}`;
        const storedData = localStorage.getItem(cacheKey);
        if (storedData) {
          const parsedData = JSON.parse(storedData);
          // 检查缓存是否过期（24小时）
          const now = new Date().getTime();
          if (
            parsedData.timestamp &&
            now - parsedData.timestamp < 24 * 60 * 60 * 1000
          ) {
            submitUsers.value = parsedData.users || {};
          }
        }
      } catch (error) {
        console.error("Error loading cached submit users:", error);
      }
    };

    // 保存序列数据到本地存储
    const saveSequenceCountsToLocalStorage = () => {
      try {
        // 按region提取subject ID
        const regionSubjectMap: Record<string, string[]> = {};
        props.subjects.forEach((subject) => {
          const region = subject.region || props.region || "head";
          if (!regionSubjectMap[region]) {
            regionSubjectMap[region] = [];
          }
          regionSubjectMap[region].push(subject.subject_id);
        });

        // 对每个region保存对应的序列数量
        Object.entries(regionSubjectMap).forEach(([region, subjectIds]) => {
          const cacheKey = `sequenceCounts_${region}`;

          // 只保存该region的数据
          const regionCounts: Record<string, number> = {};
          subjectIds.forEach((id) => {
            if (sequenceCounts.value[id] !== undefined) {
              regionCounts[id] = sequenceCounts.value[id];
            }
          });

          localStorage.setItem(
            cacheKey,
            JSON.stringify({
              counts: regionCounts,
              timestamp: new Date().getTime(),
            })
          );
        });
      } catch (error) {
        console.error("Error caching sequence counts:", error);
      }
    };

    // 保存诊断老师信息到本地存储
    const saveSubmitUsersToLocalStorage = () => {
      try {
        // 按region提取subject ID
        const regionSubjectMap: Record<string, string[]> = {};
        props.subjects.forEach((subject) => {
          const region = subject.region || props.region || "head";
          if (!regionSubjectMap[region]) {
            regionSubjectMap[region] = [];
          }
          regionSubjectMap[region].push(subject.subject_id);
        });

        // 对每个region保存对应的诊断老师信息
        Object.entries(regionSubjectMap).forEach(([region, subjectIds]) => {
          const cacheKey = `submitUsers_${region}`;

          // 只保存该region的数据
          const regionUsers: Record<string, string> = {};
          subjectIds.forEach((id) => {
            if (submitUsers.value[id] !== undefined) {
              regionUsers[id] = submitUsers.value[id];
            }
          });

          localStorage.setItem(
            cacheKey,
            JSON.stringify({
              users: regionUsers,
              timestamp: new Date().getTime(),
            })
          );
        });
      } catch (error) {
        console.error("Error caching submit users:", error);
      }
    };

    // 批量加载当前页面所有受试者的序列数量
    const loadSequencesForCurrentPage = async () => {
      if (props.subjects.length === 0) return;

      // 将受试者ID按region分组
      const subjectsByRegion: Record<string, string[]> = {};

      props.subjects.forEach((subject) => {
        const region = subject.region || props.region || "head";
        if (!subjectsByRegion[region]) {
          subjectsByRegion[region] = [];
        }
        subjectsByRegion[region].push(subject.subject_id);
      });

      // 标记所有ID为加载中
      props.subjects.forEach((subject) => {
        const id = subject.subject_id;
        if (sequenceCounts.value[id] === undefined) {
          isLoadingSequences.value[id] = true;
        }
      });

      try {
        // 按region分组请求数据
        const promises = Object.entries(subjectsByRegion).map(
          async ([region, ids]) => {
            const response = await fetchSequencesCounts(ids, region);
            return response.data || {};
          }
        );

        // 等待所有请求完成
        const results = await Promise.all(promises);

        // 合并所有结果
        const allCounts = results.reduce((acc, result) => {
          return { ...acc, ...result };
        }, {});

        // 更新计数并取消加载状态
        Object.entries(allCounts).forEach(([id, count]) => {
          sequenceCounts.value[id] = count;
          isLoadingSequences.value[id] = false;
        });

        saveSequenceCountsToLocalStorage();
      } catch (error) {
        console.error("Error loading sequence counts:", error);
        // 默认设为0并结束加载状态
        props.subjects.forEach((subject) => {
          const id = subject.subject_id;
          if (sequenceCounts.value[id] === undefined) {
            sequenceCounts.value[id] = 0;
          }
          isLoadingSequences.value[id] = false;
        });
      }
    };

    // 批量加载当前页面所有受试者的诊断老师信息
    const loadSubmitUsersForCurrentPage = async () => {
      if (props.subjects.length === 0) return;

      // 将受试者ID按region分组
      const subjectsByRegion: Record<string, string[]> = {};

      props.subjects.forEach((subject) => {
        const region = subject.region || props.region || "head";
        if (!subjectsByRegion[region]) {
          subjectsByRegion[region] = [];
        }
        subjectsByRegion[region].push(subject.subject_id);
      });

      // 标记所有ID为加载中
      props.subjects.forEach((subject) => {
        const id = subject.subject_id;
        if (submitUsers.value[id] === undefined) {
          isLoadingSubmitUsers.value[id] = true;
        }
      });

      try {
        // 按region分组请求数据
        const promises = Object.entries(subjectsByRegion).map(
          async ([region, ids]) => {
            const response = await fetchSubmitUsers(ids, region);
            return response.data || {};
          }
        );

        // 等待所有请求完成
        const results = await Promise.all(promises);

        // 合并所有结果
        const allUsers = results.reduce((acc, result) => {
          return { ...acc, ...result };
        }, {});

        // 更新诊断老师信息并取消加载状态
        Object.entries(allUsers).forEach(([id, user]) => {
          submitUsers.value[id] = user;
          isLoadingSubmitUsers.value[id] = false;
        });

        saveSubmitUsersToLocalStorage();
      } catch (error) {
        console.error("Error loading submit users:", error);
        // 默认设为空字符串并结束加载状态
        props.subjects.forEach((subject) => {
          const id = subject.subject_id;
          if (submitUsers.value[id] === undefined) {
            submitUsers.value[id] = "";
          }
          isLoadingSubmitUsers.value[id] = false;
        });
      }
    };

    // 初始化时，先从localStorage加载，然后异步请求更新
    onMounted(() => {
      loadSequenceCountsFromLocalStorage();
      loadSubmitUsersFromLocalStorage();
      loadSequencesForCurrentPage();
      loadSubmitUsersForCurrentPage();

      // 从localStorage恢复排序方向
      try {
        const savedSortDirection = localStorage.getItem("sortDirection");
        if (
          savedSortDirection &&
          (savedSortDirection === "asc" || savedSortDirection === "desc")
        ) {
          sortDirection.value = savedSortDirection;
          // 通知父组件排序方向已变更
          emit("sort-direction-change", sortDirection.value);
          console.log("已恢复排序方向:", sortDirection.value);

          // 设置一个标记，表示此次是页面加载时的恢复，而非用户手动操作
          localStorage.setItem(
            "sortDirectionRestoredAt",
            new Date().getTime().toString()
          );
        }
      } catch (error) {
        console.error("恢复排序方向失败:", error);
      }
    });

    // 当subjects或currentPage或region改变时重新加载
    watch(
      [() => props.subjects, () => props.currentPage, () => props.region],
      () => {
        loadSequenceCountsFromLocalStorage(); // 先尝试加载缓存
        loadSubmitUsersFromLocalStorage(); // 先尝试加载缓存
        loadSequencesForCurrentPage(); // 然后获取新数据
        loadSubmitUsersForCurrentPage(); // 然后获取新数据
      }
    );

    // 过滤掉subject_id为Unknown的数据
    const filteredSubjects = computed(() => {
      return props.subjects.filter(
        (subject) => subject.subject_id !== "Unknown"
      );
    });

    // 排序后的数据
    const sortedSubjects = computed(() => {
      if (props.loading) return filteredSubjects.value;

      return [...filteredSubjects.value].sort((a, b) => {
        const dateA = new Date(a.check_date).getTime();
        const dateB = new Date(b.check_date).getTime();

        return sortDirection.value === "desc"
          ? dateB - dateA // 从新到旧
          : dateA - dateB; // 从旧到新
      });
    });

    // 其他计算属性和方法...
    const displayedPages = computed(() => {
      const pages = [];
      const maxDisplayPages = 5;
      let start = Math.max(1, props.currentPage - 2);
      let end = Math.min(props.totalPages, start + maxDisplayPages - 1);

      if (end - start + 1 < maxDisplayPages) {
        start = Math.max(1, end - maxDisplayPages + 1);
      }

      for (let i = start; i <= end; i++) {
        pages.push(i);
      }

      return pages;
    });

    // 计算当前页显示的起始和结束记录
    const currentPageStartIndex = computed(() => {
      return sortedSubjects.value.length > 0
        ? (props.currentPage - 1) * props.itemsPerPage + 1
        : 0;
    });

    const currentPageEndIndex = computed(() => {
      return Math.min(
        props.currentPage * props.itemsPerPage,
        sortedSubjects.value.length
      );
    });

    // 获取当前页的数据
    const paginatedSubjects = computed(() => {
      // 前端进行分页处理
      const start = (props.currentPage - 1) * props.itemsPerPage;
      const end = Math.min(
        start + props.itemsPerPage,
        sortedSubjects.value.length
      );
      return sortedSubjects.value.slice(start, end);
    });

    // 获取行号
    function getRowNumber(index: number): number {
      return (props.currentPage - 1) * props.itemsPerPage + index + 1;
    }

    function formatDate(dateString: string): string {
      const date = new Date(dateString);
      return date.toLocaleDateString("zh-CN");
    }

    function getDiagnosisStatusText(status?: string): string {
      if (!status) return "未知";

      switch (status) {
        case "pending":
          return "未诊断";
        case "draft":
          return "已暂存";
        case "submitted":
          return "已提交";
        default:
          return "未知";
      }
    }

    function getDiagnosisStatusClass(status?: string): string {
      if (!status) return "bg-gray-100 text-gray-800";

      switch (status) {
        case "pending":
          return "bg-red-100 text-red-800";
        case "draft":
          return "bg-yellow-100 text-yellow-800";
        case "submitted":
          return "bg-green-100 text-green-800";
        default:
          return "bg-gray-100 text-gray-800";
      }
    }

    return {
      displayedPages,
      formatDate,
      sequenceCounts,
      isLoadingSequences,
      submitUsers,
      isLoadingSubmitUsers,
      getDiagnosisStatusText,
      getDiagnosisStatusClass,
      currentPageStartIndex,
      currentPageEndIndex,
      paginatedSubjects,
      getRowNumber,
      filteredSubjects,
      sortDirection,
      toggleSortDirection,
    };
  },
});
</script>

<style scoped>
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
}

.table-fixed {
  table-layout: fixed;
}

.overflow-x-auto {
  max-width: 100%;
}

/* 骨架屏动画 */
.animate-pulse {
  animation: pulse 1.5s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

@keyframes pulse {
  0%,
  100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

/* 骨架屏渐变色效果 */
.skeleton-gradient {
  position: relative;
  overflow: hidden;
}

.skeleton-gradient::after {
  content: "";
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  transform: translateX(-100%);
  background-image: linear-gradient(
    90deg,
    rgba(255, 255, 255, 0) 0,
    rgba(255, 255, 255, 0.2) 20%,
    rgba(255, 255, 255, 0.5) 60%,
    rgba(255, 255, 255, 0)
  );
  animation: shimmer 2s infinite;
}

@keyframes shimmer {
  100% {
    transform: translateX(100%);
  }
}
</style> 