<template>
  <div class="bg-white rounded-lg shadow p-6">
    <div class="mb-6">
      <h2 class="text-xl font-semibold text-gray-800 mb-2">
        {{ currentUploadType === "subjects_info" ? "受检者Excel数据上传" : currentUploadType === "images" ? "医学图像上传" : "心脏报告上传" }}
      </h2>
      <p class="text-gray-600">
        {{
          currentUploadType === "subjects_info"
            ? "上传Excel格式的受检者数据进行批量导入"
            : currentUploadType === "images"
            ? "上传Jpg或png格式的医学影像文件"
            : "上传心脏检查报告文件"
        }}
      </p>
    </div>

    <!-- Excel上传部分 -->
    <div v-if="currentUploadType === 'subjects_info'" class="space-y-6">
      <div
        class="border-2 border-dashed border-gray-300 rounded-lg p-8 text-center hover:border-blue-500 transition-colors duration-200"
        :class="{ 'border-blue-500 bg-blue-50': isDraggingExcel }"
        @dragover.prevent="isDraggingExcel = true"
        @dragleave.prevent="isDraggingExcel = false"
        @drop.prevent="handleExcelDrop"
      >
        <div class="flex flex-col items-center">
          <svg
            xmlns="http://www.w3.org/2000/svg"
            class="h-12 w-12 text-gray-400 mb-4"
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
          >
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              stroke-width="1.5"
              d="M9 17v-2m3 2v-4m3 4v-6m2 10H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
            />
          </svg>
          <p class="text-lg font-medium mb-1">拖放Excel文件到此处</p>
          <p class="text-sm text-gray-500 mb-4">或</p>
          <label
            class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 cursor-pointer"
          >
            <span>选择文件</span>
            <input
              type="file"
              class="hidden"
              accept=".xlsx"
              @change="handleExcelFileChange"
            />
          </label>
        </div>
      </div>

      <div class="bg-gray-50 p-4 rounded-md">
        <h3 class="font-medium text-gray-700 mb-2">上传说明</h3>
        <ul class="list-disc list-inside text-sm text-gray-600 space-y-1">
          <li>
            请参照该
            <a
              href="http://**************:83/upload/examination.html"
              target="_blank"
              class="text-blue-600 hover:underline"
              >说明文档</a
            >
            进行自动化生成
          </li>
        
        <div class="mt-3">
          <li>
            若要手工录入，请使用模板（不推荐）
            <a
              href="https://oss-dicom.oss-cn-beijing.aliyuncs.com/%E5%AF%BC%E5%85%A5%E6%A8%A1%E6%9D%BF.xlsx?OSSAccessKeyId=LTAI5tJjvX12mB8GSG9vSMB8&Expires=1777390520&Signature=OAK773luRFsM4a1IFFtBk9IG0pw%3D"
              class="text-blue-600 hover:underline text-sm"
            >下载Excel导入模板</a
          >
          </li>
        </div>
       </ul>
          
        
      </div>
    </div>

    <!-- 图像上传部分 -->
    <div v-else-if="currentUploadType === 'images'" class="space-y-6">
      <div
        class="border-2 border-dashed border-gray-300 rounded-lg p-8 text-center hover:border-blue-500 transition-colors duration-200"
      >
        <div class="flex flex-col items-center">
          <svg
            xmlns="http://www.w3.org/2000/svg"
            class="h-12 w-12 text-gray-400 mb-4"
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
          >
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              stroke-width="1.5"
              d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"
            />
          </svg>
          <label
            class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 cursor-pointer"
          >
            <span>选择文件夹</span>
            <input
              type="file"
              class="hidden"
              webkitdirectory
              directory
              multiple
              @change="handleImageFolderChange"
            />
          </label>
        </div>
      </div>

      <div class="bg-gray-50 p-4 rounded-md">
        <h3 class="font-medium text-gray-700 mb-2">上传说明</h3>
        <ul class="list-disc list-inside text-sm text-gray-600 space-y-1">
          <li>
            请参照该
            <a
              href="http://**************:83/upload/examination.html"
              target="_blank"
              class="text-blue-600 hover:underline"
              >说明文档</a
            >
            进行自动化生成
          </li>
        </ul>
      </div>

      <!-- 上传进度显示 -->
      <div v-if="uploadProgress > 0 && uploadProgress < 100" class="mt-4">
        <div class="w-full bg-gray-200 rounded-full h-4">
          <div
            class="bg-blue-600 h-4 rounded-full transition-all duration-300 ease-in-out"
            :style="{ width: `${uploadProgress}%` }"
          ></div>
        </div>
        <div class="text-center mt-2 text-sm text-gray-600">
          正在上传 {{ uploadProgress.toFixed(1) }}%
        </div>
      </div>
    </div>

    <!-- 心脏报告上传部分 -->
    <div v-else-if="currentUploadType === 'heart_report'" class="space-y-6">
      <div
        class="border-2 border-dashed border-gray-300 rounded-lg p-8 text-center hover:border-blue-500 transition-colors duration-200"
        :class="{ 'border-blue-500 bg-blue-50': isDraggingHeartReport }"
        @dragover.prevent="isDraggingHeartReport = true"
        @dragleave.prevent="isDraggingHeartReport = false"
        @drop.prevent="handleHeartReportDrop"
      >
        <div class="flex flex-col items-center">
          <svg
            xmlns="http://www.w3.org/2000/svg"
            class="h-12 w-12 text-gray-400 mb-4"
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
          >
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              stroke-width="1.5"
              d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"
            />
          </svg>
          <p class="text-lg font-medium mb-1">拖放心脏报告CSV文件到此处</p>
          <p class="text-sm text-gray-500 mb-4">或</p>
          <label
            class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 cursor-pointer"
          >
            <span>选择文件</span>
            <input
              type="file"
              class="hidden"
              accept=".csv"
              multiple
              @change="handleHeartReportFileChange"
            />
          </label>
        </div>
      </div>

      <div class="bg-gray-50 p-4 rounded-md">
        <h3 class="font-medium text-gray-700 mb-2">上传说明</h3>
        <ul class="list-disc list-inside text-sm text-gray-600 space-y-1">
          <li>仅支持CSV格式文件,并且文件的命名是受检者的PID，一定要准确录入！</li>
          <li>支持同时选择多个文件上传</li>
          <li>
            请参照该
            <a
              href="http://**************:83/upload/examination.html"
              target="_blank"
              class="text-blue-600 hover:underline"
              >说明文档</a
            >
          </li>
        </ul>
      </div>

      <!-- 文件上传进度显示 -->
      <div v-if="uploadProgress > 0 && uploadProgress < 100" class="mt-4">
        <div class="w-full bg-gray-200 rounded-full h-4">
          <div
            class="bg-blue-600 h-4 rounded-full transition-all duration-300 ease-in-out"
            :style="{ width: `${uploadProgress}%` }"
          ></div>
        </div>
        <div class="text-center mt-2 text-sm text-gray-600">
          正在上传 {{ uploadProgress.toFixed(1) }}%
        </div>
      </div>

      <!-- 上传文件列表 -->
      <div v-if="heartReportFiles.length > 0" class="mt-4">
        <h4 class="font-medium text-gray-700 mb-2">待上传文件</h4>
        <div class="space-y-2">
          <div
            v-for="(file, index) in heartReportFiles"
            :key="index"
            class="flex items-center justify-between p-2 bg-gray-50 rounded"
          >
            <span class="text-sm text-gray-600">{{ file.name }}</span>
            <button
              @click="removeHeartReportFile(index)"
              class="text-red-500 hover:text-red-700"
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                class="h-5 w-5"
                viewBox="0 0 20 20"
                fill="currentColor"
              >
                <path
                  fill-rule="evenodd"
                  d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z"
                  clip-rule="evenodd"
                />
              </svg>
            </button>
          </div>
        </div>
        <div class="mt-4 flex justify-end">
          <button
            @click="uploadHeartReportFiles"
            class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
            :disabled="isUploading"
          >
            {{ isUploading ? '上传中...' : '开始上传' }}
          </button>
        </div>
      </div>
    </div>

    <!-- 上传状态提示 -->
    <div v-if="uploadStatus" class="mt-6 p-4 rounded-md" :class="statusClass">
      <div class="flex items-center">
        <span v-if="uploadStatus === 'success'" class="mr-2">
          <svg
            xmlns="http://www.w3.org/2000/svg"
            class="h-5 w-5"
            viewBox="0 0 20 20"
            fill="currentColor"
          >
            <path
              fill-rule="evenodd"
              d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z"
              clip-rule="evenodd"
            />
          </svg>
        </span>
        <span v-else-if="uploadStatus === 'error'" class="mr-2">
          <svg
            xmlns="http://www.w3.org/2000/svg"
            class="h-5 w-5"
            viewBox="0 0 20 20"
            fill="currentColor"
          >
            <path
              fill-rule="evenodd"
              d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z"
              clip-rule="evenodd"
            />
          </svg>
        </span>
        <span>{{ statusMessage }}</span>
      </div>
    </div>

    <!-- 上传处理中状态显示 -->
    <div
      v-if="isUploading && !uploadProgress"
      class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50"
    >
      <div class="bg-white p-6 rounded-lg shadow-lg text-center">
        <svg
          class="animate-spin h-10 w-10 text-blue-600 mx-auto mb-4"
          xmlns="http://www.w3.org/2000/svg"
          fill="none"
          viewBox="0 0 24 24"
        >
          <circle
            class="opacity-25"
            cx="12"
            cy="12"
            r="10"
            stroke="currentColor"
            stroke-width="4"
          ></circle>
          <path
            class="opacity-75"
            fill="currentColor"
            d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
          ></path>
        </svg>
        <p class="text-gray-700 font-medium">文件上传中，请稍候...</p>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import { defineComponent, ref, computed } from "vue";
import { uploadExcelFile, uploadImageFiles, uploadHeartReportFile } from "../../services/upload";

export default defineComponent({
  name: "DataUploadContent",

  props: {
    currentUploadType: {
      type: String as () => "subjects_info" | "images" | "heart_report",
      required: true,
    },
    checkPart: {
      type: String,
      required: true,
    },
    loading: {
      type: Boolean,
      default: false,
    },
  },

  emits: [
    "upload-excel",
    "upload-images",
    "upload-heart-report",
    "upload-start",
    "upload-complete",
    "upload-error",
  ],

  setup(props, { emit }) {
    const isDraggingExcel = ref(false);
    const isDraggingHeartReport = ref(false);
    const uploadStatus = ref<null | "success" | "error">(null);
    const statusMessage = ref("");
    const isUploading = ref(false);
    const uploadProgress = ref(0);
    const heartReportFiles = ref<File[]>([]);

    const statusClass = computed(() => {
      if (uploadStatus.value === "success") {
        return "bg-green-50 text-green-700";
      } else if (uploadStatus.value === "error") {
        return "bg-red-50 text-red-700";
      }
      return "";
    });

    // Excel文件拖拽处理
    function handleExcelDrop(event: DragEvent) {
      isDraggingExcel.value = false;
      if (!event.dataTransfer?.files.length) return;

      const file = event.dataTransfer.files[0];
      if (isValidExcelFile(file)) {
        handleExcelUpload(file);
      } else {
        setErrorStatus("请上传有效的Excel文件 (.xlsx)");
      }
    }

    // Excel文件选择处理
    function handleExcelFileChange(event: Event) {
      const target = event.target as HTMLInputElement;
      if (!target.files?.length) return;

      const file = target.files[0];
      if (isValidExcelFile(file)) {
        handleExcelUpload(file);
      } else {
        setErrorStatus("请上传有效的Excel文件 (.xlsx)");
      }

      target.value = "";
    }

    // 处理Excel文件上传
    async function handleExcelUpload(file: File) {
      try {
        setUploading();
        const response = await uploadExcelFile(file);
        setSuccessStatus("Excel文件上传成功");
        emit("upload-excel", response);
        emit("upload-complete", response);
      } catch (error) {
        console.error("Excel上传失败", error);
        setErrorStatus("Excel上传失败，请重试");
        emit("upload-error", error);
      }
    }

    // 验证Excel文件
    function isValidExcelFile(file: File): boolean {
      return file.name.endsWith(".xlsx");
    }

    // 图像文件夹选择处理
    function handleImageFolderChange(event: Event) {
      const target = event.target as HTMLInputElement;
      if (!target.files?.length) return;

      // 获取文件夹名称（根文件夹）
      const firstFile = target.files[0];
      const pathParts = firstFile.webkitRelativePath.split("/");
      const folderName = pathParts[0];

      // 检查文件是否为有效的图像文件
      const files = Array.from(target.files);
      const validFiles = files.filter(isValidImageFile);

      if (validFiles.length === 0) {
        setErrorStatus("未找到有效的图像文件");
        target.value = "";
        return;
      }

      // 使用文件夹名称作为检查部位
      handleImageFolderUpload(target.files, folderName);
      target.value = "";
    }

    // 验证图像文件
    function isValidImageFile(file: File): boolean {
      const validExtensions = [".jpg", ".jpeg", ".png"];
      return validExtensions.some((ext) =>
        file.name.toLowerCase().endsWith(ext)
      );
    }

    // 处理图像文件夹上传
    async function handleImageFolderUpload(
      files: FileList,
      folderName: string
    ) {
      if (files.length === 0) return;

      try {
        setUploading();

        const totalFiles = files.length;
        let completedFiles = 0;
        let successCount = 0;
        let failedCount = 0;
        const maxRetries = 3; // 最大重试次数
        const failedFiles = new Set(); // 记录失败的文件
        const retryMap = new Map(); // 记录每个文件的重试次数

        // 创建一个完整的文件数组
        const allFiles = Array.from(files);

        // 设置最大并发数
        const maxConcurrent = 20; // 最多同时上传20个文件

        // 进度更新函数
        const updateProgress = () => {
          completedFiles++;
          uploadProgress.value = (completedFiles / totalFiles) * 100;
        };

        // 单个文件上传函数（包含重试逻辑）
        async function uploadSingleFile(
          file: File,
          retryCount = 0
        ): Promise<boolean> {
          try {
            const dataTransfer = new DataTransfer();
            dataTransfer.items.add(file);
            const response = await uploadImageFiles(
              dataTransfer.files,
              folderName
            );

            if (response.success) {
              successCount++;
              failedFiles.delete(file.name); // 如果重试成功，从失败集合中移除
              return true;
            }

            throw new Error(response.results[0]?.error || "上传失败");
          } catch (error) {
            console.error(
              `文件 ${file.name} 上传失败 (尝试 ${
                retryCount + 1
              }/${maxRetries}):`,
              error
            );

            // 如果还可以重试
            if (retryCount < maxRetries - 1) {
              console.log(`准备重试文件 ${file.name}`);
              // 延迟重试，避免立即重试可能遇到的临时性问题
              await new Promise((resolve) =>
                setTimeout(resolve, 1000 * (retryCount + 1))
              );
              return uploadSingleFile(file, retryCount + 1);
            }

            // 超过重试次数，记录失败
            failedCount++;
            failedFiles.add(file.name);
            return false;
          } finally {
            // 只在最后一次尝试时更新进度
            if (retryCount === 0) {
              updateProgress();
            }
          }
        }

        // 用于控制并发数的函数
        async function uploadWithConcurrencyLimit() {
          // 将文件分组，每组maxConcurrent个
          const groups = [];
          for (let i = 0; i < allFiles.length; i += maxConcurrent) {
            groups.push(allFiles.slice(i, i + maxConcurrent));
          }

          // 按组顺序上传，每组内的文件并发上传
          for (const group of groups) {
            if (!isUploading.value) break; // 用户取消上传

            // 并发上传当前组中的所有文件
            await Promise.all(group.map((file) => uploadSingleFile(file)));
          }
        }

        // 开始上传
        await uploadWithConcurrencyLimit();

        // 处理完成状态
        if (failedCount === 0) {
          setSuccessStatus(`成功上传 ${successCount} 个图像文件`);
          emit("upload-images", { success: true, totalFiles: successCount });
          emit("upload-complete", response);
        } else {
          const failedFilesList = Array.from(failedFiles).join(", ");
          setErrorStatus(
            `上传完成：${successCount} 个成功，${failedCount} 个失败。\n失败文件：${failedFilesList}`
          );
          emit("upload-images", {
            success: successCount > 0,
            totalFiles: successCount,
            failedCount,
            failedFiles: Array.from(failedFiles),
          });
          emit("upload-complete", response);
        }
      } catch (error) {
        console.error("图像上传失败", error);
        setErrorStatus("图像上传失败，请重试");
        emit("upload-error", error);
      } finally {
        // 重置进度
        setTimeout(() => {
          uploadProgress.value = 0;
        }, 2000);
      }
    }

    // 处理拖放上传
    function handleHeartReportDrop(event: DragEvent) {
      isDraggingHeartReport.value = false;
      if (!event.dataTransfer?.files.length) return;

      const files = Array.from(event.dataTransfer.files);
      const validFiles = files.filter(isValidHeartReportFile);
      
      if (validFiles.length === 0) {
        setErrorStatus("请上传有效的CSV格式文件");
        return;
      }

      heartReportFiles.value.push(...validFiles);
    }

    // 处理文件选择
    function handleHeartReportFileChange(event: Event) {
      const target = event.target as HTMLInputElement;
      if (!target.files?.length) return;

      const files = Array.from(target.files);
      const validFiles = files.filter(isValidHeartReportFile);

      if (validFiles.length === 0) {
        setErrorStatus("请上传有效的CSV格式文件");
      } else {
        heartReportFiles.value.push(...validFiles);
      }

      target.value = ""; // 重置input
    }

    // 移除文件
    function removeHeartReportFile(index: number) {
      heartReportFiles.value.splice(index, 1);
    }

    // 上传所有文件
    async function uploadHeartReportFiles() {
      if (heartReportFiles.value.length === 0) return;

      try {
        setUploading();
        const totalFiles = heartReportFiles.value.length;
        let completedFiles = 0;
        let successCount = 0;
        let failedFiles: string[] = [];

        // 并发上传文件
        const results = await Promise.all(
          heartReportFiles.value.map(async (file) => {
            try {
              const response = await uploadHeartReportFile(file);
              completedFiles++;
              uploadProgress.value = (completedFiles / totalFiles) * 100;
              
              if (response.success) {
                successCount++;
                return response;
              } else {
                failedFiles.push(file.name);
                return response;
              }
            } catch (error) {
              failedFiles.push(file.name);
              return {
                success: false,
                results: [{
                  name: file.name,
                  error: error instanceof Error ? error.message : '上传失败'
                }]
              };
            }
          })
        );

        // 处理上传结果
        if (failedFiles.length === 0) {
          setSuccessStatus(`成功上传 ${successCount} 个文件`);
          emit("upload-heart-report", { success: true, results });
        } else {
          setErrorStatus(
            `上传完成：${successCount} 个成功，${failedFiles.length} 个失败。\n失败文件：${failedFiles.join(", ")}`
          );
          emit("upload-heart-report", {
            success: successCount > 0,
            results,
            failedFiles
          });
        }

        // 清空文件列表
        heartReportFiles.value = [];
      } catch (error) {
        console.error("文件上传失败", error);
        setErrorStatus("文件上传失败，请重试");
        emit("upload-error", error);
      } finally {
        // 重置进度
        setTimeout(() => {
          uploadProgress.value = 0;
        }, 2000);
      }
    }

    // 验证心脏报告文件
    function isValidHeartReportFile(file: File): boolean {
      return file.name.toLowerCase().endsWith('.csv');
    }

    // 状态管理函数
    function setUploading() {
      isUploading.value = true;
      uploadStatus.value = null;
      statusMessage.value = "";
      uploadProgress.value = 0;
      emit("upload-start");
    }

    function setSuccessStatus(message: string) {
      isUploading.value = false;
      uploadStatus.value = "success";
      statusMessage.value = message;
    }

    function setErrorStatus(message: string) {
      isUploading.value = false;
      uploadStatus.value = "error";
      statusMessage.value = message;
    }

    return {
      isDraggingExcel,
      isDraggingHeartReport,
      uploadStatus,
      statusMessage,
      statusClass,
      isUploading,
      uploadProgress,
      heartReportFiles,
      removeHeartReportFile,
      uploadHeartReportFiles,
      handleExcelDrop,
      handleExcelFileChange,
      handleImageFolderChange,
      handleHeartReportDrop,
      handleHeartReportFileChange,
    };
  },
});
</script> 