<template>
  <div>
    <!-- 患者表格 -->
    <div class="bg-white rounded-lg shadow overflow-hidden">
      <div
        class="p-6 border-b border-gray-200 flex flex-wrap justify-between items-center"
      >
        <div class="mb-2 sm:mb-0">
          <h2 class="text-lg font-semibold text-gray-800">所有患者</h2>
          <p class="text-sm text-gray-500">筛选日期患者数</p>
        </div>
        <div class="flex flex-wrap items-center">
          <div
            class="flex flex-wrap items-center mb-2 sm:mb-0 w-full sm:w-auto"
          >
            <span class="mr-2 text-sm text-gray-600">筛选日期:</span>
            <div class="relative mr-1">
              <input
                type="date"
                v-model="startDate"
                class="border border-gray-300 rounded px-3 py-1 text-sm cursor-pointer focus:border-purple-500 focus:ring-1 focus:ring-purple-500 focus:outline-none w-full"
                placeholder="开始日期"
              />
            </div>
            <span class="mx-1 text-sm text-gray-600">至</span>
            <div class="relative mr-3">
              <input
                type="date"
                v-model="endDate"
                class="border border-gray-300 rounded px-3 py-1 text-sm cursor-pointer focus:border-purple-500 focus:ring-1 focus:ring-purple-500 focus:outline-none w-full"
                placeholder="结束日期"
              />
            </div>
            <button
              @click="filterByDateRange"
              class="bg-purple-600 text-white px-3 py-1 rounded-md text-sm hover:bg-purple-700 transition-colors"
            >
              筛选
            </button>
          </div>
        </div>
      </div>

      <!-- 统计卡片 - 现在放在所有患者标题和表格之间 -->
      <div class="p-4 border-b border-gray-200">
        <div class="flex flex-wrap gap-4">
          <!-- 总患者数卡片 -->
          <div
            class="bg-white rounded-lg border border-gray-200 shadow-sm p-4 flex items-center flex-grow basis-52 max-w-xs"
          >
            <div class="rounded-full bg-blue-100 p-3 mr-4">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                class="h-6 w-6 text-blue-600"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  stroke-width="2"
                  d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"
                />
              </svg>
            </div>
            <div>
              <div class="text-2xl font-bold">{{ stats.totalPatients }}</div>
              <div class="text-gray-500 text-sm">总患者数</div>
            </div>
          </div>

          <!-- 诊断进度卡片 -->
          <div
            class="bg-white rounded-lg border border-gray-200 shadow-sm p-4 flex items-center flex-grow basis-52 max-w-xs"
          >
            <div class="rounded-full bg-green-100 p-3 mr-4">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                class="h-6 w-6 text-green-600"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  stroke-width="2"
                  d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"
                />
              </svg>
            </div>
            <div>
              <div class="text-2xl font-bold">
                {{ stats.diagnosedCount }}/{{ stats.totalDiagnosisCount }}
              </div>
              <div class="text-gray-500 text-sm">已诊断/总需诊断条数</div>
            </div>
          </div>

          <!-- 头部待诊断卡片 -->
          <div
            class="bg-white rounded-lg border border-gray-200 shadow-sm p-4 flex items-center flex-grow basis-52 max-w-xs"
          >
            <div class="rounded-full bg-blue-100 p-3 mr-4">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                class="h-6 w-6 text-blue-600"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  stroke-width="2"
                  d="M9 3v2m6-2v2M9 19v2m6-2v2M5 9H3m2 6H3m18-6h-2m2 6h-2M7 19h10a2 2 0 002-2V7a2 2 0 00-2-2H7a2 2 0 00-2 2v10a2 2 0 002 2zM9 9h6v6H9V9z"
                />
              </svg>
            </div>
            <div>
              <div class="text-2xl font-bold">
                {{ stats.regionCounts.head || stats.regionCounts.brain || 0 }}
              </div>
              <div class="text-gray-500 text-sm">头部待诊断</div>
            </div>
          </div>

          <!-- 腹部待诊断卡片 -->
          <div
            class="bg-white rounded-lg border border-gray-200 shadow-sm p-4 flex items-center flex-grow basis-52 max-w-xs"
          >
            <div class="rounded-full bg-green-100 p-3 mr-4">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                class="h-6 w-6 text-green-600"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  stroke-width="2"
                  d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"
                />
              </svg>
            </div>
            <div>
              <div class="text-2xl font-bold">
                {{ stats.regionCounts.abdomen || 0 }}
              </div>
              <div class="text-gray-500 text-sm">腹部待诊断</div>
            </div>
          </div>

          <!-- 其他部位待诊断卡片 -->
          <div
            class="bg-white rounded-lg border border-gray-200 shadow-sm p-4 flex items-center flex-grow basis-52 max-w-xs"
          >
            <div class="rounded-full bg-gray-100 p-3 mr-4">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                class="h-6 w-6 text-gray-600"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  stroke-width="2"
                  d="M12 4v1m6 11h2m-6 0h-2v4m0-11v3m0 0h.01M12 12h4.01M16 20h4M4 12h4m12 0h.01M5 8h2a1 1 0 001-1V5a1 1 0 00-1-1H5a1 1 0 00-1 1v2a1 1 0 001 1zm12 0h2a1 1 0 001-1V5a1 1 0 00-1-1h-2a1 1 0 00-1 1v2a1 1 0 001 1zM5 20h2a1 1 0 001-1v-2a1 1 0 00-1-1H5a1 1 0 00-1 1v2a1 1 0 001 1z"
                />
              </svg>
            </div>
            <div>
              <div class="text-2xl font-bold">{{ getOtherRegionsCount() }}</div>
              <div class="text-gray-500 text-sm">其他部位待诊断</div>
            </div>
          </div>
        </div>
      </div>

      <patient-table
        :subjects="subjects"
        :loading="loading"
        :region="currentRegion"
        :total-subjects="totalSubjects"
        :current-page="currentPage"
        :total-pages="totalPages"
        :items-per-page="100"
        @edit-subject="navigateToSubject"
        @change-page="handlePageChange"
        @sort-direction-change="handleSortDirectionChange"
      />
    </div>
  </div>
</template>

<script lang="ts">
import { defineComponent, ref, computed, watch, onMounted } from "vue";
import { useRouter } from "vue-router";
import PatientTable from "./PatientTable.vue";
import { fetchStats } from "../../services/report";

export default defineComponent({
  name: "CheckRegionsContent",

  components: {
    PatientTable,
  },

  props: {
    subjects: {
      type: Array,
      required: true,
    },
    loading: {
      type: Boolean,
      default: false,
    },
    currentRegion: {
      type: String,
      required: true,
    },
    dateRange: {
      type: Object,
      default: () => ({
        startDate: "",
        endDate: "",
      }),
    },
    doctorFirstName: {
      type: String,
      default: "",
    },
    currentPage: {
      type: Number,
      default: 1,
    },
    totalPages: {
      type: Number,
      default: 1,
    },
    totalSubjects: {
      type: Number,
      required: true,
    },
  },

  emits: [
    "filter-date",
    "edit-subject",
    "change-page",
    "sort-direction-change",
  ],

  setup(props, { emit }) {
    const router = useRouter();
    const startDate = ref(props.dateRange.startDate || "");
    const endDate = ref(props.dateRange.endDate || "");

    // 新增统计数据对象
    const stats = ref({
      totalPatients: 0,
      diagnosedCount: 0,
      totalDiagnosisCount: 0,
      regionCounts: {} as Record<string, number>,
    });

    // 获取统计数据
    const getStats = async () => {
      try {
        // 如果有日期筛选，则传递日期参数
        const params =
          startDate.value && endDate.value
            ? {
                start_date: startDate.value,
                end_date: endDate.value,
              }
            : undefined;

        const response = await fetchStats(params);
        stats.value = response;
      } catch (error) {
        console.error("获取统计数据失败:", error);
        // 添加默认值处理，确保UI不会因为错误而崩溃
        stats.value = {
          totalPatients: 0,
          diagnosedCount: 0,
          totalDiagnosisCount: 0,
          regionCounts: {},
        };
      }
    };

    // 组件挂载时获取统计数据
    onMounted(() => {
      getStats();
    });

    // 监听 props.dateRange 变化，更新本地日期
    watch(
      () => props.dateRange,
      (newDateRange) => {
        startDate.value = newDateRange.startDate || "";
        endDate.value = newDateRange.endDate || "";
      },
      { deep: true }
    );

    // 监听日期筛选变化，重新获取统计数据
    watch(
      [startDate, endDate],
      () => {
        if (startDate.value && endDate.value) {
          getStats();
        }
      },
      { deep: true }
    );

    function filterByDateRange() {
      emit("filter-date", {
        startDate: startDate.value,
        endDate: endDate.value,
      });
      // 筛选后重新获取统计数据
      getStats();
    }

    function navigateToSubject(subjectId: string, region: string) {
      // 保存当前状态到sessionStorage
      try {
        // 获取准确的滚动位置
        const scrollPosition =
          window.scrollY ||
          document.documentElement.scrollTop ||
          document.body.scrollTop;

        const positionInfo = {
          view: "regions", // 当前视图是regions
          region: props.currentRegion,
          page: props.currentPage,
          sortDirection: localStorage.getItem("sortDirection") || "desc",
          scrollPosition: scrollPosition,
          timestamp: new Date().getTime(),
        };

        // 如果滚动位置是0或接近0，检查是否应该保存一个默认位置
        if (scrollPosition < 50) {
          console.log("滚动位置接近0，尝试获取表格位置");

          // 尝试获取患者表格的位置作为参考
          const tableElement =
            document.querySelector(".patient-table") ||
            document.querySelector("table") ||
            document.querySelector(".overflow-x-auto");

          if (tableElement) {
            const tablePosition =
              tableElement.getBoundingClientRect().top + window.scrollY;
            if (tablePosition > 50) {
              positionInfo.scrollPosition = tablePosition - 30; // 设置为表格上方一点的位置
              console.log(
                "使用表格位置作为滚动参考:",
                positionInfo.scrollPosition
              );
            }
          }
        }

        console.log("保存位置信息:", positionInfo);
        sessionStorage.setItem(
          "diagnosisReturnPosition",
          JSON.stringify(positionInfo)
        );
      } catch (err) {
        console.error("保存位置信息失败:", err);
      }

      // 导航到诊断页面时携带区域参数和返回URL
      router.push({
        path: `/diagnosis/${subjectId}`,
        query: {
          region: region === "others" ? props.currentRegion : region,
          returnTo: "dashboard",
          returnPage: props.currentPage.toString(),
          _t: new Date().getTime().toString(), // 添加时间戳，避免路由缓存
        },
      });
    }

    function handlePageChange(page: number) {
      emit("change-page", page);
    }

    function getOtherRegionsCount() {
      // 计算除头部和腹部外的其他部位待诊断总数
      let otherCount = 0;

      // 遍历所有部位统计
      for (const [region, count] of Object.entries(stats.value.regionCounts)) {
        // 排除头部和腹部
        if (region !== "head" && region !== "brain" && region !== "abdomen") {
          otherCount += count;
        }
      }

      return otherCount;
    }

    function handleSortDirectionChange(direction: "asc" | "desc") {
      // 将排序方向变化事件传递给父组件
      emit("sort-direction-change", direction);
    }

    return {
      startDate,
      endDate,
      stats,
      filterByDateRange,
      navigateToSubject,
      handlePageChange,
      getOtherRegionsCount,
      handleSortDirectionChange,
    };
  },
});
</script>

<style scoped>
/* 可以添加组件特定的样式 */
</style> 