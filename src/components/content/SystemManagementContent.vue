<template>
  <div>
    <!-- 显示设置部分 - 移到前面 -->
    <div class="bg-white rounded-lg shadow mb-6">
      <div class="p-4">
        <h2 class="text-lg mb-4">
          显示设置
          <span class="text-gray-500 text-sm"
            >（设置后其他用户会默认显示该时间段内的待诊断数据）</span
          >
        </h2>

        <div class="space-y-4">
          <div class="flex items-center">
            <label class="w-24 text-sm text-gray-600">检查日期</label>
            <div class="flex items-center space-x-2">
              <input
                type="date"
                v-model="dateRange.start"
                class="border border-gray-300 rounded-md px-3 py-1.5 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
              <span class="text-gray-500">至</span>
              <input
                type="date"
                v-model="dateRange.end"
                class="border border-gray-300 rounded-md px-3 py-1.5 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
              <button
                @click="saveDateRange"
                class="ml-4 bg-blue-500 text-white px-4 py-1.5 rounded-md text-sm hover:bg-blue-600"
                :disabled="saving"
              >
                {{ saving ? "保存中..." : "保存" }}
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 用户列表部分 -->
    <div class="bg-white rounded-lg shadow">
      <div class="p-4 flex justify-between items-center">
        <h2 class="text-lg">用户列表</h2>
        <button
          @click="showUserModal = true"
          class="bg-blue-500 text-white px-4 py-2 rounded-md text-sm hover:bg-blue-600 flex items-center"
        >
          <span class="mr-1">+</span>
          新增用户
        </button>
      </div>

      <div class="overflow-x-auto">
        <table class="min-w-full">
          <thead class="bg-gray-50">
            <tr>
              <th class="px-6 py-3 text-left text-sm text-gray-500">用户名</th>
              <th class="px-6 py-3 text-left text-sm text-gray-500">姓名</th>
              <th class="px-6 py-3 text-left text-sm text-gray-500">角色</th>
              <th class="px-6 py-3 text-left text-sm text-gray-500">状态</th>
              <th class="px-6 py-3 text-left text-sm text-gray-500">操作</th>
            </tr>
          </thead>
          <tbody class="bg-white divide-y divide-gray-200">
            <tr v-for="user in users" :key="user.username">
              <td class="px-6 py-4 text-sm">{{ user.username }}</td>
              <td class="px-6 py-4 text-sm">{{ user.name }}</td>
              <td class="px-6 py-4 text-sm">{{ user.role }}</td>
              <td class="px-6 py-4">
                <span
                  :class="[
                    'px-2 py-1 text-sm rounded-md',
                    isUserActive(user)
                      ? 'bg-green-50 text-green-600'
                      : 'bg-red-50 text-red-600',
                  ]"
                >
                  {{ isUserActive(user) ? "启用" : "禁用" }}
                </span>
              </td>
              <td class="px-6 py-4">
                <button
                  @click="emit('edit-user', user.username)"
                  class="text-blue-600 hover:text-blue-800 mr-4"
                >
                  编辑
                </button>
                <button
                  @click="emit('toggle-user-status', user.username)"
                  class="text-red-600 hover:text-red-800"
                >
                  {{ isUserActive(user) ? "禁用" : "启用" }}
                </button>
              </td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>

    <!-- 新增用户对话框 -->
    <div
      v-if="showUserModal"
      class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50"
    >
      <div class="bg-white rounded-lg shadow-lg p-6 w-full max-w-md">
        <h3 class="text-lg font-medium mb-4">
          {{ isEditMode ? "编辑用户" : "新增用户" }}
        </h3>

        <div class="space-y-4">
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-1"
              >用户名</label
            >
            <input
              type="text"
              v-model="userForm.username"
              :disabled="isEditMode"
              class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
              placeholder="请输入用户名"
            />
          </div>

          <div>
            <label class="block text-sm font-medium text-gray-700 mb-1"
              >姓名</label
            >
            <input
              type="text"
              v-model="userForm.name"
              class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
              placeholder="请输入姓名"
            />
          </div>

          <div>
            <label class="block text-sm font-medium text-gray-700 mb-1"
              >密码</label
            >
            <input
              type="password"
              v-model="userForm.password"
              class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
              placeholder="请输入密码"
            />
          </div>

          <div>
            <label class="block text-sm font-medium text-gray-700 mb-1"
              >角色</label
            >
            <select
              v-model="userForm.role"
              class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="user">普通用户</option>
              <option value="admin">管理员</option>
            </select>
          </div>
        </div>

        <div class="flex justify-end space-x-3 mt-6">
          <button
            @click="showUserModal = false"
            class="px-4 py-2 border border-gray-300 rounded-md text-sm hover:bg-gray-50"
          >
            取消
          </button>
          <button
            @click="saveUser"
            :disabled="isSaving"
            class="px-4 py-2 bg-blue-500 text-white rounded-md text-sm hover:bg-blue-600"
          >
            {{ isSaving ? "保存中..." : "保存" }}
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from "vue";
import type { User } from "@/types/system";
import {
  createUser,
  saveDateRangeSettings,
  getDateRangeSettings,
} from "@/services/system";

// 定义DateRange类型
interface DateRange {
  start: string;
  end: string;
}

interface Props {
  users: User[];
  initialDateRange?: DateRange;
}

const props = withDefaults(defineProps<Props>(), {
  initialDateRange: () => ({
    start: "",
    end: new Date().toISOString().split('T')[0], // 设置end为今天日期
  }),
});

const dateRange = ref(props.initialDateRange);
const saving = ref(false);

// 用户表单状态
const showUserModal = ref(false);
const isEditMode = ref(false);
const isSaving = ref(false);
const userForm = ref({
  username: "",
  name: "",
  password: "",
  role: "user",
});

// 重置用户表单
function resetUserForm() {
  userForm.value = {
    username: "",
    name: "",
    password: "",
    role: "user",
  };
  isEditMode.value = false;
}

// 保存用户
async function saveUser() {
  if (
    !userForm.value.username ||
    !userForm.value.name ||
    (!isEditMode.value && !userForm.value.password)
  ) {
    alert("请填写所有必填字段");
    return;
  }

  isSaving.value = true;
  try {
    await createUser({
      username: userForm.value.username,
      name: userForm.value.name,
      password: userForm.value.password,
      role: userForm.value.role,
    });

    showUserModal.value = false;
    resetUserForm();
    emit("user-added");
  } catch (error: any) {
    alert(error.response?.data?.detail || "创建用户失败");
  } finally {
    isSaving.value = false;
  }
}

// 加载日期范围设置
async function loadDateRangeSettings() {
  try {
    const settings = await getDateRangeSettings();
    dateRange.value = {
      start: settings.start_date || "",
      end: settings.end_date || new Date().toISOString().split('T')[0], // 如果后端没有返回end_date，使用今天日期
    };
  } catch (error) {
    console.error("加载日期范围设置失败", error);

  }
}

// 保存日期范围设置
async function saveDateRange() {
  // 验证开始日期和结束日期不能为空
  if (!dateRange.value.start || !dateRange.value.end) {
    alert('请填写完整的日期范围');
    return;
  }

  saving.value = true;
  try {
    await saveDateRangeSettings(dateRange.value);
    alert('保存成功');
  } catch (error) {
    console.error("保存日期范围设置失败", error);
    alert('保存失败，请重试');
  } finally {
    saving.value = false;
  }
}

// 判断用户是否处于激活状态
function isUserActive(user: User): boolean {
  // 适配后端返回的状态字段，is_disabled=false表示激活，is_disabled=true表示禁用
  if ("active" in user && user.active !== undefined) {
    return user.active;
  } else if ("is_disabled" in user && user.is_disabled !== undefined) {
    return !user.is_disabled;
  }
  return false;
}

// 组件挂载时加载日期范围设置
onMounted(() => {
  loadDateRangeSettings();
});

const emit = defineEmits<{
  (e: "add-user"): void;
  (e: "edit-user", id: string): void;
  (e: "toggle-user-status", id: string): void;
  (e: "save-date-settings", range: DateRange): void;
  (e: "user-added"): void;
}>();
</script> 