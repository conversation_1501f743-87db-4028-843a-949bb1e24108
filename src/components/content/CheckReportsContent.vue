<template>
  <div class="container">
    <!-- 报告筛选与列表卡片 -->
    <div class="bg-white rounded-xl shadow-lg mb-6 overflow-hidden">
      <!-- 标题部分 -->
      <div class="px-6 py-5 border-b border-gray-100">
        <h2 class="text-lg font-semibold text-gray-800">报告筛选与查看</h2>
      </div>

      <!-- 日期筛选部分 -->
      <div class="p-6 bg-blue-50 bg-opacity-30">
        <div class="grid grid-cols-1 md:grid-cols-3 gap-5">
          <div class="flex flex-col">
            <label class="text-sm font-medium text-gray-700 mb-1.5"
              >开始日期</label
            >
            <div class="relative group">
              <input
                type="date"
                v-model="dateRange.start"
                class="date-input w-full px-4 py-2.5 border border-gray-200 rounded-lg shadow-sm focus:border-blue-400 focus:ring-2 focus:ring-blue-100 outline-none transition-all duration-200 cursor-pointer appearance-none"
                onfocus="this.showPicker()"
              />
              <div
                class="absolute inset-y-0 right-0 flex items-center pr-3 text-gray-400 group-hover:text-blue-500 transition-colors duration-200"
              >
                <svg
                  class="w-5 h-5"
                  viewBox="0 0 24 24"
                  fill="none"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    d="M8 2V5"
                    stroke="currentColor"
                    stroke-width="1.5"
                    stroke-miterlimit="10"
                    stroke-linecap="round"
                    stroke-linejoin="round"
                  />
                  <path
                    d="M16 2V5"
                    stroke="currentColor"
                    stroke-width="1.5"
                    stroke-miterlimit="10"
                    stroke-linecap="round"
                    stroke-linejoin="round"
                  />
                  <path
                    d="M3.5 9.09H20.5"
                    stroke="currentColor"
                    stroke-width="1.5"
                    stroke-miterlimit="10"
                    stroke-linecap="round"
                    stroke-linejoin="round"
                  />
                  <path
                    d="M21 8.5V17C21 20 19.5 22 16 22H8C4.5 22 3 20 3 17V8.5C3 5.5 4.5 3.5 8 3.5H16C19.5 3.5 21 5.5 21 8.5Z"
                    stroke="currentColor"
                    stroke-width="1.5"
                    stroke-miterlimit="10"
                    stroke-linecap="round"
                    stroke-linejoin="round"
                  />
                  <path
                    d="M11.995 13.7H12.005"
                    stroke="currentColor"
                    stroke-width="2"
                    stroke-linecap="round"
                    stroke-linejoin="round"
                  />
                  <path
                    d="M8.294 13.7H8.304"
                    stroke="currentColor"
                    stroke-width="2"
                    stroke-linecap="round"
                    stroke-linejoin="round"
                  />
                  <path
                    d="M8.294 16.7H8.304"
                    stroke="currentColor"
                    stroke-width="2"
                    stroke-linecap="round"
                    stroke-linejoin="round"
                  />
                </svg>
              </div>
            </div>
          </div>
          <div class="flex flex-col">
            <label class="text-sm font-medium text-gray-700 mb-1.5"
              >结束日期</label
            >
            <div class="relative group">
              <input
                type="date"
                v-model="dateRange.end"
                class="date-input w-full px-4 py-2.5 border border-gray-200 rounded-lg shadow-sm focus:border-blue-400 focus:ring-2 focus:ring-blue-100 outline-none transition-all duration-200 cursor-pointer appearance-none"
                onfocus="this.showPicker()"
              />
              <div
                class="absolute inset-y-0 right-0 flex items-center pr-3 text-gray-400 group-hover:text-blue-500 transition-colors duration-200"
              >
                <svg
                  class="w-5 h-5"
                  viewBox="0 0 24 24"
                  fill="none"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    d="M8 2V5"
                    stroke="currentColor"
                    stroke-width="1.5"
                    stroke-miterlimit="10"
                    stroke-linecap="round"
                    stroke-linejoin="round"
                  />
                  <path
                    d="M16 2V5"
                    stroke="currentColor"
                    stroke-width="1.5"
                    stroke-miterlimit="10"
                    stroke-linecap="round"
                    stroke-linejoin="round"
                  />
                  <path
                    d="M3.5 9.09H20.5"
                    stroke="currentColor"
                    stroke-width="1.5"
                    stroke-miterlimit="10"
                    stroke-linecap="round"
                    stroke-linejoin="round"
                  />
                  <path
                    d="M21 8.5V17C21 20 19.5 22 16 22H8C4.5 22 3 20 3 17V8.5C3 5.5 4.5 3.5 8 3.5H16C19.5 3.5 21 5.5 21 8.5Z"
                    stroke="currentColor"
                    stroke-width="1.5"
                    stroke-miterlimit="10"
                    stroke-linecap="round"
                    stroke-linejoin="round"
                  />
                  <path
                    d="M11.995 13.7H12.005"
                    stroke="currentColor"
                    stroke-width="2"
                    stroke-linecap="round"
                    stroke-linejoin="round"
                  />
                  <path
                    d="M8.294 13.7H8.304"
                    stroke="currentColor"
                    stroke-width="2"
                    stroke-linecap="round"
                    stroke-linejoin="round"
                  />
                  <path
                    d="M8.294 16.7H8.304"
                    stroke="currentColor"
                    stroke-width="2"
                    stroke-linecap="round"
                    stroke-linejoin="round"
                  />
                </svg>
              </div>
            </div>
          </div>
          <div class="flex flex-col justify-end">
            <p v-if="loadedFromSettings" class="text-xs text-gray-500 mb-2">
              * 已从系统设置加载默认日期范围
            </p>
          </div>
        </div>

        <div class="flex justify-between items-center mt-5">
          <div class="flex gap-3">
            <button
              @click="fetchReportsData"
              class="px-5 py-2.5 bg-blue-600 text-white rounded-lg hover:bg-blue-700 focus:ring-4 focus:ring-blue-100 transition-all duration-200 font-medium"
            >
              应用筛选
            </button>
            <button
              v-if="isFiltered"
              @click="clearFilters"
              class="px-5 py-2.5 border border-gray-200 text-gray-700 rounded-lg hover:bg-gray-50 focus:ring-4 focus:ring-gray-100 transition-all duration-200 font-medium"
            >
              清除筛选
            </button>
          </div>
        </div>
      </div>

      <!-- 报告列表 -->
      <div class="px-6 py-5 border-t border-gray-100">
        <div class="flex justify-between items-center mb-4">
          <h2 class="text-lg font-semibold text-gray-800">报告列表</h2>

          <!-- 批量下载按钮 -->
          <div class="flex items-center gap-3">
            <button
              v-if="selectedReports.length > 0"
              @click="toggleSelectAll"
              class="text-sm text-blue-600 hover:underline"
            >
              {{ isAllSelected ? "取消全选" : "全选" }}
            </button>
            <button
              @click="handleBatchDownload"
              :disabled="selectedReports.length === 0 || batchLoading"
              :class="[
                'inline-flex items-center px-4 py-2 rounded-lg text-sm font-medium transition-all duration-200',
                selectedReports.length === 0 || batchLoading
                  ? 'bg-gray-100 text-gray-400 cursor-not-allowed'
                  : 'bg-blue-600 text-white hover:bg-blue-700 focus:ring-4 focus:ring-blue-100',
              ]"
            >
              <svg
                v-if="!batchLoading"
                class="w-4 h-4 mr-2"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  stroke-width="2"
                  d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4"
                ></path>
              </svg>
              <svg
                v-else
                class="animate-spin -ml-1 mr-2 h-4 w-4 text-white"
                xmlns="http://www.w3.org/2000/svg"
                fill="none"
                viewBox="0 0 24 24"
              >
                <circle
                  class="opacity-25"
                  cx="12"
                  cy="12"
                  r="10"
                  stroke="currentColor"
                  stroke-width="4"
                ></circle>
                <path
                  class="opacity-75"
                  fill="currentColor"
                  d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                ></path>
              </svg>
              批量下载{{
                selectedReports.length ? `(${selectedReports.length})` : ""
              }}
            </button>
          </div>
        </div>

        <!-- 批量下载进度条 -->
        <div
          v-if="batchProgress.current > 0"
          class="mb-5 bg-blue-50 p-4 rounded-lg"
        >
          <div class="flex items-center justify-between mb-2">
            <div class="text-sm font-medium text-blue-700">
              {{
                batchStatusText ||
                `正在处理: ${batchProgress.current}/${batchProgress.total} 份报告`
              }}
            </div>
            <div class="text-sm text-blue-600">
              {{
                Math.round((batchProgress.current / batchProgress.total) * 100)
              }}%
            </div>
          </div>
          <div class="w-full bg-gray-200 rounded-full h-2.5">
            <div
              class="bg-blue-600 h-2.5 rounded-full transition-all duration-300 ease-in-out"
              :style="{
                width: `${
                  (batchProgress.current / batchProgress.total) * 100
                }%`,
              }"
            ></div>
          </div>
        </div>

        <div class="overflow-hidden rounded-lg border border-gray-200">
          <!-- 表头 -->
          <div
            class="grid grid-cols-7 px-6 py-4 bg-gray-50 text-sm font-medium text-gray-600"
          >
            <div class="flex items-center">
              <input
                type="checkbox"
                :checked="isAllSelected"
                @change="toggleSelectAll"
                class="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 focus:ring-2"
              />
            </div>
            <div>患者姓名</div>
            <div>检查项目</div>
            <div>报告状态</div>
            <div>报告详情</div>
            <div>检查时间</div>
            <div>操作</div>
          </div>

          <!-- 表格内容 -->
          <div v-if="reports.length > 0">
            <div
              v-for="report in reports"
              :key="report.subject_id"
              class="grid grid-cols-7 px-6 py-4 border-t border-gray-200 hover:bg-blue-50 items-center transition-colors duration-150"
            >
              <div class="flex items-center">
                <input
                  type="checkbox"
                  :disabled="report.status !== 1"
                  v-model="selectedReports"
                  :value="report.subject_id"
                  class="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 focus:ring-2 disabled:bg-gray-200 disabled:opacity-50"
                />
              </div>
              <div class="text-gray-900 font-medium">
                {{ report.name }}
              </div>
              <div class="text-gray-700">{{ getPartName(report.regions) }}</div>
              <div>
                <div
                  :class="[
                    'inline-flex items-center px-3 py-1 rounded-full text-sm',
                    report.status === 1
                      ? 'bg-green-100 text-green-700'
                      : 'bg-yellow-100 text-yellow-700',
                  ]"
                >
                  <span
                    :class="[
                      'mr-1.5 w-2 h-2 rounded-full',
                      report.status === 1 ? 'bg-green-500' : 'bg-yellow-500',
                    ]"
                  ></span>
                  {{ report.status === 1 ? "已完成" : "未完成" }}
                </div>
              </div>
              <div class="text-gray-700">
                {{ report.status_text }}
              </div>
              <div class="text-gray-700">
                {{ formatDate(report.check_date) }}
              </div>
              <div>
                <button
                  @click="handleDownload(report)"
                  :disabled="report.status !== 1"
                  class="px-3 py-1.5 bg-blue-600 text-white rounded-lg hover:bg-blue-700 focus:ring-2 focus:ring-blue-100 transition-all duration-200 text-sm disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  下载
                </button>
              </div>
            </div>
          </div>

          <!-- 如果没有数据，显示空状态 -->
          <div
            v-else-if="!loading"
            class="px-6 py-12 text-center text-gray-500"
          >
            <svg
              class="w-12 h-12 mx-auto text-gray-300 mb-3"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                stroke-linecap="round"
                stroke-linejoin="round"
                stroke-width="1.5"
                d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
              ></path>
            </svg>
            <p class="text-lg">暂无报告数据</p>
            <p class="text-sm text-gray-400 mt-1">请选择日期范围并应用筛选</p>
          </div>

          <!-- 加载状态 -->
          <div v-if="loading" class="px-6 py-12 text-center text-gray-500">
            <div
              class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500 mb-3"
            ></div>
            <p>正在加载数据...</p>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import { defineComponent, ref, onMounted, computed } from "vue";
import { getDateRangeSettings } from "../../services/system";
import {
  fetchReports,
  downloadReport,
  batchDownloadReports,
} from "../../services/report";

export default defineComponent({
  name: "CheckReportsContent",

  props: {
    loading: {
      type: Boolean,
      default: false,
    },
  },

  emits: ["edit-report"],

  setup(props, { emit }) {
    // 日期筛选
    const dateRange = ref({
      start: "",
      end: "",
    });

    // 筛选状态
    const isFiltered = ref(false);
    const loading = ref(false);
    const reports = ref<any[]>([]);
    const loadedFromSettings = ref(false);

    // 多选相关状态
    const selectedReports = ref<string[]>([]);
    const isAllSelected = computed(() => {
      const completedReports = reports.value.filter((r) => r.status === 1);
      return (
        completedReports.length > 0 &&
        selectedReports.value.length === completedReports.length
      );
    });

    // 批量下载状态
    const batchLoading = ref(false);
    const batchProgress = ref({
      current: 0,
      total: 0,
    });
    // 添加等待提示文本
    const batchStatusText = ref("");

    // 获取报告列表
    async function fetchReportsData() {
      try {
        loading.value = true;
        isFiltered.value = true;
        // 重置选择
        selectedReports.value = [];

        const params = {
          start_date: dateRange.value.start,
          end_date: dateRange.value.end,
        };

        const response = await fetchReports(params);
        reports.value = response.reports || [];
      } catch (error) {
        console.error("获取报告失败:", error);
      } finally {
        loading.value = false;
      }
    }

    // 清除筛选条件
    function clearFilters() {
      dateRange.value.start = "";
      dateRange.value.end = "";
      isFiltered.value = false;
      loadedFromSettings.value = false;
      reports.value = [];
      selectedReports.value = [];
    }

    // 查看报告
    function viewReport(report: any) {
      emit("edit-report", report.subject_id);
    }

    // 下载报告
    async function handleDownload(report: any) {
      try {
        loading.value = true;
        const response = await downloadReport(report.subject_id);

        // 创建下载链接
        const url = window.URL.createObjectURL(response);
        const link = document.createElement("a");

        // 设置文件名：患者姓名-检查项目-检查日期.pdf
        const fileName = `${report.name}-${formatDate(
          report.check_date
        )}-检查报告.pdf`;

        link.href = url;
        link.setAttribute("download", fileName);
        document.body.appendChild(link);
        link.click();

        // 清理
        window.URL.revokeObjectURL(url);
        document.body.removeChild(link);
      } catch (error) {
        console.error("下载报告失败:", error);
        alert("下载报告失败，请稍后重试");
      } finally {
        loading.value = false;
      }
    }

    // 切换全选/取消全选
    function toggleSelectAll() {
      if (isAllSelected.value) {
        // 取消全选
        selectedReports.value = [];
      } else {
        // 全选（只选择状态为已完成的报告）
        selectedReports.value = reports.value
          .filter((report) => report.status === 1)
          .map((report) => report.subject_id);
      }
    }

    // 批量下载报告
    async function handleBatchDownload() {
      if (selectedReports.value.length === 0 || batchLoading.value) {
        return;
      }

      try {
        batchLoading.value = true;

        // 设置进度初始值
        batchProgress.value = {
          current: 0,
          total: selectedReports.value.length,
        };

        // 设置初始状态文本
        batchStatusText.value = "正在准备文件...";

        // 进度模拟部分修改为三个阶段
        // 1. 初始准备阶段 - 慢速增长到20%
        // 2. 处理中阶段 - 慢速增长到60%
        // 3. 下载完成阶段 - 迅速到100%

        // 准备阶段
        const preparationInterval = setInterval(() => {
          if (
            batchProgress.value.current <
            Math.floor(batchProgress.value.total * 0.2)
          ) {
            batchProgress.value.current += 1;
            batchStatusText.value = "正在准备文件...";
          } else {
            clearInterval(preparationInterval);
            // 处理中阶段
            batchStatusText.value = "服务器正在生成报告文件，处理大量数据需要较长时间，请耐心等待...";
          }
        }, 1000);

        // 设置服务器处理阶段的进度模拟
        const processingInterval = setInterval(() => {
          if (
            batchProgress.value.current <
            Math.floor(batchProgress.value.total * 0.6)
          ) {
            // 处理阶段缓慢增加进度
            batchProgress.value.current += 1;
            
            // 根据进度更新提示文本
            if (batchProgress.value.current >= Math.floor(batchProgress.value.total * 0.4)) {
              batchStatusText.value = "正在压缩文件并准备下载，请继续等待...";
            }
          } else {
            clearInterval(processingInterval);
          }
        }, 2000);

        // 发送批量下载请求
        const response = await batchDownloadReports(selectedReports.value);

        // 清除所有模拟进度定时器
        clearInterval(preparationInterval);
        clearInterval(processingInterval);

        // 下载阶段 - 快速完成到100%
        batchStatusText.value = "文件已生成，正在下载...";
        batchProgress.value.current = batchProgress.value.total;

        // 创建下载链接
        const url = window.URL.createObjectURL(response);
        const link = document.createElement("a");

        // 设置文件名
        const fileName = `批量检查报告_${
          new Date().toISOString().split("T")[0]
        }.zip`;

        link.href = url;
        link.setAttribute("download", fileName);
        document.body.appendChild(link);
        link.click();

        // 清理
        window.URL.revokeObjectURL(url);
        document.body.removeChild(link);

        // 延迟重置进度显示
        setTimeout(() => {
          batchProgress.value = { current: 0, total: 0 };
          batchStatusText.value = "";
        }, 3000);
      } catch (error) {
        console.error("批量下载报告失败:", error);
        batchStatusText.value = "下载失败，请稍后重试";
        // 根据错误类型提供不同的错误提示
        const err = error as { message?: string };
        if (err.message && err.message.includes("timeout")) {
          alert(
            "下载超时，服务器可能正在处理大量文件，请耐心等待或减少选择的文件数量"
          );
        } else {
          alert("批量下载报告失败，请稍后重试");
        }

        // 5秒后重置状态
        setTimeout(() => {
          batchProgress.value = { current: 0, total: 0 };
          batchStatusText.value = "";
        }, 5000);
      } finally {
        batchLoading.value = false;
      }
    }

    function formatDate(dateString: string): string {
      const date = new Date(dateString);
      return date.toLocaleDateString("zh-CN");
    }

    function getPartName(parts: string[]): string {
      const partNames: { [key: string]: string } = {
        head: "头部",
        abdomen: "腹部",
        cspine: "颈椎",
        lspine: "腰椎",
        heart: "心脏",
        knee: "膝关节",
        pelvis: "盆腔",
        others: "其他",
      };

      if (!parts || parts.length === 0) return "无";

      return parts.map((part) => partNames[part] || part).join(", ");
    }

    // 加载日期范围设置
    async function loadDateRangeSettings() {
      try {
        const settings = await getDateRangeSettings();
        if (settings && settings.start_date && settings.end_date) {
          dateRange.value.start = settings.start_date;
          dateRange.value.end = settings.end_date;
          // 标记日期已从设置加载
          loadedFromSettings.value = true;
          // 不再自动执行筛选
          // fetchReportsData();
        }
      } catch (error) {
        console.error("加载日期范围设置失败:", error);
      }
    }

    // 初始化加载
    onMounted(() => {
      loadDateRangeSettings();
    });

    return {
      dateRange,
      isFiltered,
      reports,
      loading,
      loadedFromSettings,
      selectedReports,
      isAllSelected,
      batchLoading,
      batchProgress,
      batchStatusText,
      fetchReportsData,
      clearFilters,
      viewReport,
      handleDownload,
      handleBatchDownload,
      toggleSelectAll,
      formatDate,
      getPartName,
    };
  },
});
</script>

<style scoped>
.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 2rem;
}

/* 日期选择器样式 */
.date-input::-webkit-calendar-picker-indicator {
  opacity: 0;
  width: 100%;
  height: 100%;
  position: absolute;
  right: 0;
  cursor: pointer;
}
</style> 