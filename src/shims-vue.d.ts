declare module '*.vue' {
    import type { DefineComponent } from 'vue'
    const component: DefineComponent<{}, {}, any>
    export default component
}

// 声明 Vue 模板中使用的内部变量
declare global {
    namespace JSX {
        interface IntrinsicElements {
            [elem: string]: any
        }
    }

    // 声明 Volar 使用的内部变量
    const __VLS_intrinsicElements: Record<string, any>;
    const __VLS_elementAsFunctionalComponent: Record<string, any>;
    const __VLS_functionalComponentArgsRest: any;
    const __VLS_pickFunctionalComponentCtx: any;
    const __VLS_asFunctionalComponent: any;
    const __VLS_NormalizeEmits: any;
} 