import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { login as apiLogin, checkAuthStatus } from '@/services/auth'

interface User {
    username: string
    name: string
}

interface LoginCredentials {
    username: string
    password: string
    remember: boolean
}

export const useAuthStore = defineStore('auth', () => {
    const user = ref<User | null>(null)
    const token = ref<string | null>(null)
    const savedCredentials = ref<{ username: string, password: string } | null>(null)

    // Load user info from localStorage on initialization
    const storedUser = localStorage.getItem('user')
    const storedToken = localStorage.getItem('token')
    const storedCredentials = localStorage.getItem('savedCredentials')

    if (storedUser && storedToken) {
        user.value = JSON.parse(storedUser)
        token.value = storedToken
    }

    if (storedCredentials) {
        savedCredentials.value = JSON.parse(storedCredentials)
    }

    const isAuthenticated = computed(() => !!token.value)

    // Validate auth status with the backend
    async function validateAuth() {
        if (token.value) {
            const isValid = await checkAuthStatus()
            if (!isValid) {
                logout()
            }
            return isValid
        }
        return false
    }

    async function login(credentials: LoginCredentials) {
        try {
            const response = await apiLogin(credentials)
            user.value = response.user
            token.value = response.token

            if (credentials.remember) {
                localStorage.setItem('user', JSON.stringify(response.user))
                localStorage.setItem('token', response.token)

                // Save username and password when remember is checked
                const credentialsToSave = {
                    username: credentials.username,
                    password: credentials.password
                }
                localStorage.setItem('savedCredentials', JSON.stringify(credentialsToSave))
                savedCredentials.value = credentialsToSave
            } else {
                // Clear saved credentials if remember is not checked
                localStorage.removeItem('savedCredentials')
                savedCredentials.value = null
            }

            return true
        } catch (error) {
            console.error('Login failed:', error)
            return false
        }
    }

    function logout() {
        user.value = null
        token.value = null
        localStorage.removeItem('user')
        localStorage.removeItem('token')
    }

    return {
        user,
        token,
        savedCredentials,
        isAuthenticated,
        login,
        logout,
        validateAuth
    }
}) 