import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import {
    fetchSubjects,
    fetchSubjectById,
    fetchDiagnosisResult,
    saveDiagnosticResult,
    fetchImageSequences,
    Subject,
    DiagnosisResult,
    SubjectsFilter,
    ImageSequence
} from '@/services/diagnosis'
import { useAuthStore } from '@/store/auth'

export const useDiagnosisStore = defineStore('diagnosis', () => {
    const subjects = ref<Subject[]>([])
    const currentSubject = ref<Subject | null>(null)
    const currentDiagnosis = ref<DiagnosisResult | null>(null)
    const loading = ref(false)
    const error = ref('')

    // Image sequence state
    const sequences = ref<ImageSequence[]>([])
    const currentSequenceIndex = ref(0)
    const currentImageIndex = ref(0)
    // 添加预加载图像缓存
    const preloadedImages = ref<Map<string, boolean>>(new Map())

    async function loadSubjects(filter: SubjectsFilter) {
        loading.value = true
        error.value = ''
        try {
            const allSubjects = await fetchSubjects(filter)
            subjects.value = allSubjects
            console.log(subjects.value)
        } catch (err) {
            error.value = '加载患者列表失败'
            console.error(err)
        } finally {
            loading.value = false
        }
    }

    async function loadSubjectById(subject_id: string) {
        loading.value = true
        error.value = ''
        try {
            currentSubject.value = await fetchSubjectById(subject_id)
        } catch (err) {
            error.value = '加载患者详情失败'
            console.error(err)
        } finally {
            loading.value = false
        }
    }

    async function loadDiagnosisResult(subjectId: string, region?: string) {
        loading.value = true
        error.value = ''
        try {
            currentDiagnosis.value = await fetchDiagnosisResult(subjectId, region)
        } catch (err) {
            error.value = '加载诊断结果失败'
            console.error(err)
        } finally {
            loading.value = false
        }
    }

    // 添加更新诊断结果的方法
    async function updateDiagnosis(diagnosisData: {
        subject_id: string;
        result: string;
        check_date: string;
        region: string;
        status: string;
    }): Promise<boolean> {
        loading.value = true
        error.value = ''

        try {
            // 获取当前登录用户名
            const authStore = useAuthStore()
            if (!authStore.user || !authStore.user.username) {
                error.value = '未登录或用户会话已过期'
                return false
            }

            // 添加用户名到诊断数据
            const fullDiagnosisData = {
                ...diagnosisData,
                username: authStore.user.username
            }

            // 调用保存诊断结果的API
            const result = await saveDiagnosticResult(fullDiagnosisData)

            // 更新当前诊断结果
            currentDiagnosis.value = result

            return true
        } catch (err: any) {
            error.value = err.message || '保存诊断结果失败'
            console.error('Error updating diagnosis:', err)
            return false
        } finally {
            loading.value = false
        }
    }

    async function loadImageSequences(subjectId: string, region: string) {
        loading.value = true
        error.value = ''
        try {
            sequences.value = await fetchImageSequences(subjectId, region)
            // Reset image indices
            if (sequences.value.length > 0) {
                currentSequenceIndex.value = 0
                currentImageIndex.value = 0

                // 初始预加载
                if (currentSequence.value && currentSequence.value.images) {
                    preloadImages(currentSequenceIndex.value, currentImageIndex.value)
                }
            }
            return sequences.value
        } catch (err) {
            error.value = '加载图像序列失败'
            console.error(err)
            return []
        } finally {
            loading.value = false
        }
    }

    // 添加预加载函数
    function preloadImages(sequenceIndex: number, imageIndex: number) {
        if (!sequences.value[sequenceIndex] || !sequences.value[sequenceIndex].images) {
            return;
        }

        const images = sequences.value[sequenceIndex].images;
        const totalImages = images.length;

        // 修改预加载范围：从当前图像开始及后面的图像
        const start = imageIndex; // 从当前图像开始
        const end = Math.min(totalImages - 1, imageIndex + 10); // 最多加载后面10张

        // 创建预加载任务数组
        const preloadTasks: Promise<void>[] = [];

        for (let i = start; i <= end; i++) {
            const imageUrl = images[i];

            // 检查是否已经预加载过
            if (!preloadedImages.value.has(imageUrl)) {
                // 创建预加载Promise并添加到任务数组
                const preloadTask = new Promise<void>((resolve) => {
                    const img = new Image();
                    img.onload = () => {
                        preloadedImages.value.set(imageUrl, true);
                        console.log(`预加载图像完成: ${i + 1}/${totalImages}`);
                        resolve();
                    };
                    img.onerror = () => {
                        console.error(`预加载图像失败: ${imageUrl}`);
                        resolve(); // 即使失败也resolve，避免阻塞其他图像加载
                    };
                    img.src = imageUrl;
                });

                preloadTasks.push(preloadTask);
                // 将URL标记为已尝试加载，即使尚未完成
                preloadedImages.value.set(imageUrl, false);
            }
        }

        // 并发执行所有预加载任务
        if (preloadTasks.length > 0) {
            console.log(`开始并发预加载 ${preloadTasks.length} 张图像`);
            Promise.all(preloadTasks).then(() => {
                console.log('所有图像预加载完成');
            });
        }
    }

    // Navigation methods for image sequences
    function selectSequence(index: number) {
        if (index >= 0 && index < sequences.value.length) {
            currentSequenceIndex.value = index
            currentImageIndex.value = 0 // Reset image index
            // 预加载新序列的图像
            preloadImages(currentSequenceIndex.value, currentImageIndex.value)
        }
    }

    function nextImage() {
        const maxIndex = currentSequence.value?.images?.length ? currentSequence.value.images.length - 1 : 0
        if (currentImageIndex.value < maxIndex) {
            currentImageIndex.value++
            // 预加载下一批图像
            preloadImages(currentSequenceIndex.value, currentImageIndex.value)
        }
    }

    function prevImage() {
        if (currentImageIndex.value > 0) {
            currentImageIndex.value--
            // 预加载上一批图像
            preloadImages(currentSequenceIndex.value, currentImageIndex.value)
        }
    }

    // Computed properties
    const currentSequence = computed(() => {
        if (sequences.value.length === 0) return null
        return sequences.value[currentSequenceIndex.value]
    })

    const currentImage = computed(() => {
        if (!currentSequence.value || !currentSequence.value.images || currentSequence.value.images.length === 0) return null
        return currentSequence.value.images[currentImageIndex.value]
    })

    const canGoToPrevImage = computed(() => currentImageIndex.value > 0)

    const canGoToNextImage = computed(() => {
        if (!currentSequence.value || !currentSequence.value.images || !Array.isArray(currentSequence.value.images)) return false
        const imagesLength = currentSequence.value.images.length
        return currentImageIndex.value < (imagesLength - 1)
    })

    return {
        subjects,
        currentSubject,
        currentDiagnosis,
        loading,
        error,
        loadSubjects,
        loadSubjectById,
        loadDiagnosisResult,
        updateDiagnosis,
        // Image sequence state
        sequences,
        currentSequenceIndex,
        currentImageIndex,
        currentSequence,
        currentImage,
        canGoToPrevImage,
        canGoToNextImage,
        // Image sequence functions
        loadImageSequences,
        selectSequence,
        nextImage,
        prevImage,
        // 添加预加载函数到导出
        preloadImages
    }
}) 