import api from './api'

interface Report {
    subject_id: string,
    name: string,
    regions: string[],
    status: number,
    status_text: string,
    check_date: string
}

interface ReportResponse {
    reports: Report[]
}

interface ReportParams {
    start_date: string
    end_date: string
}

// 统计数据接口定义
interface StatsData {
    totalPatients: number,
    diagnosedCount: number,
    totalDiagnosisCount: number,
    regionCounts: Record<string, number>
}

/**
 * 获取报告列表
 * @param params 查询参数
 * @returns 报告列表数据
 */
export async function fetchReports(params: ReportParams): Promise<ReportResponse> {
    return await api.get('/reports', { params })
}

/**
 * 下载报告
 * @param subjectId 患者ID
 * @returns 下载响应
 */
export async function downloadReport(subjectId: string): Promise<Blob> {
    return await api.get(`/reports/${subjectId}/download`, {
        responseType: 'blob'
    })
}

/**
 * 批量下载报告
 * @param subjectIds 患者ID列表
 * @returns 下载响应（ZIP文件Blob）
 */
export async function batchDownloadReports(subjectIds: string[]): Promise<Blob> {
    return await api.post('/reports/batch-download', {
        subject_ids: subjectIds
    }, {
        responseType: 'blob',
        timeout: 300000  // 增加超时时间到5分钟
    })
}

/**
 * 获取统计数据
 * @param params 查询参数（可选）
 * @returns 统计数据
 */
export async function fetchStats(params?: ReportParams): Promise<StatsData> {
    return await api.get('/stats', {
        params: params ? {
            start_date: params.start_date,
            end_date: params.end_date
        } : undefined
    })
}
