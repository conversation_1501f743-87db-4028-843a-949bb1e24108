import api from './api'

export interface User {
    id?: string;
    username: string;
    name: string;
    role: string;
    active?: boolean;
    is_disabled?: boolean;  // Backend returns is_disabled=false for active, is_disabled=true for inactive
    lastLogin?: string;
    createdAt?: string;
    updatedAt?: string;
}

export interface UserCreateData {
    username: string
    password: string
    name: string
    role: string
}

export interface DisplaySettings {
    displayDays: number
    dateRange: {
        start: string
        end: string
    }
}

export interface DateRangeSettings {
    start_date: string
    end_date: string
}

export interface SystemConfig {
    key: string
    value: any
    type: string
    description?: string
}

// 获取用户列表
export async function fetchUsers(): Promise<User[]> {
    return await api.get(`/system/users`)
}

// 创建新用户
export async function createUser(userData: UserCreateData): Promise<User> {
    return await api.post(`/system/users`, userData)
}

// 更新用户状态
export async function updateUserStatus(userId: string, active: boolean): Promise<User> {
    return await api.patch(`/system/users/${userId}/status`, { active })
}

// 更新用户信息
export async function updateUser(userId: string, userData: Partial<UserCreateData>): Promise<User> {
    return await api.put(`/system/users/${userId}`, userData)
}

// 保存显示设置
export async function saveDisplaySettings(settings: DisplaySettings): Promise<void> {
    await api.post(`/system/settings/display`, settings)
}

// 获取显示设置
export async function getDisplaySettings(): Promise<DisplaySettings> {
    return await api.get(`/system/settings/display`)
}

// 保存日期范围设置
export async function saveDateRangeSettings(dateRange: { start: string, end: string }): Promise<void> {
    await api.post(`/system/settings/date_range`, {
        start_date: dateRange.start,
        end_date: dateRange.end
    })
}

// 获取日期范围设置
export async function getDateRangeSettings(): Promise<DateRangeSettings> {
    const response: { value: DateRangeSettings } = await api.get(`/system/settings/date_range`)
    return response.value
}

// 获取所有系统配置
export async function getAllSystemConfigs(): Promise<SystemConfig[]> {
    return await api.get(`/system/config`)
}

// 获取特定系统配置
export async function getSystemConfig(key: string): Promise<SystemConfig> {
    return await api.get(`/system/config/${key}`)
}

// 保存系统配置
export async function saveSystemConfig(key: string, value: any): Promise<SystemConfig> {
    return await api.post(`/system/config/${key}`, { value })
} 