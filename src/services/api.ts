import axios from 'axios'
import { useAuthStore } from '@/store/auth'

// Create Axios instance with base configuration
const api = axios.create({
    baseURL: import.meta.env.VITE_API_URL || '/api/v1', // Default to /api/v1 if env var not set
    timeout: 60000,
    headers: {
        'Content-Type': 'application/json'
    }
})

// Request interceptor - add auth token to requests
api.interceptors.request.use(
    config => {
        const authStore = useAuthStore()
        if (authStore.token) {
            config.headers.Authorization = `Bearer ${authStore.token}`
        }
        return config
    },
    error => {
        return Promise.reject(error)
    }
)

// Response interceptor - handle common errors and extract data
api.interceptors.response.use(
    response => {
        // Return data directly for easier use in components
        return response.data
    },
    error => {
        // Handle unauthorized errors (401) by logging out
        if (error.response && error.response.status === 401) {
            const authStore = useAuthStore()
            authStore.logout()
            window.location.href = '/login'
        }
        return Promise.reject(error)
    }
)

export default api 