import api from './api'

export interface LoginCredentials {
    username: string
    password: string
    remember: boolean
}

export interface LoginResponse {
    user: {
        username: string
        name: string
    }
    token: string
}

/**
 * Logs in a user with the provided credentials
 * Uses the /login endpoint as specified in the requirements
 */
export async function login(credentials: LoginCredentials): Promise<LoginResponse> {
    try {
        const { remember, ...loginData } = credentials
        return await api.post('/login', loginData)
    } catch (error) {
        console.error('Login failed:', error);
        throw new Error('登录失败，请检查用户名和密码');
    }
}

// Check if user is already logged in
export async function checkAuthStatus(): Promise<boolean> {
    try {
        // This endpoint should be implemented by the backend to validate the token
        await api.get('/auth/check')
        return true
    } catch (error) {
        return false
    }
} 