import api from './api'
import OSS from 'ali-oss'

interface STSToken {
    accessKeyId: string;
    accessKeySecret: string;
    securityToken: string;
    expiration: string;
    region: string;
    bucket: string;
    endpoint: string;
}

// 添加缓存机制
let cachedToken: STSToken | null = null;
let tokenExpirationTime: number | null = null;

/**
 * 获取OSS上传Token
 */
async function getSTSToken(): Promise<STSToken> {
    // 检查缓存中的Token是否可用
    const now = Date.now();

    // 如果有缓存的Token且未过期（留出30秒的安全时间），直接返回
    if (cachedToken && tokenExpirationTime && now < tokenExpirationTime - 30000) {
        console.log("使用缓存的STS Token");
        return cachedToken;
    }

    // 否则重新获取Token
    console.log("获取新的STS Token");
    const response = await api.post('/upload/sts-token');
    cachedToken = response.data;

    // 计算过期时间（转为毫秒时间戳）
    if (cachedToken && cachedToken.expiration) {
        tokenExpirationTime = new Date(cachedToken.expiration).getTime();
    }

    return response.data;
}

/**
 * 上传Excel文件
 * @param file Excel文件
 * @returns 服务器响应
 */
export async function uploadExcelFile(file: File) {
    const formData = new FormData()
    formData.append('file', file)

    return api.post('/upload/excel', formData, {
        headers: {
            'Content-Type': 'multipart/form-data'
        }
    })
}

/**
 * 获取OSS客户端实例
 */
async function getOSSClient() {
    const stsToken = await getSTSToken();
    return new OSS({
        accessKeyId: stsToken.accessKeyId,
        accessKeySecret: stsToken.accessKeySecret,
        stsToken: stsToken.securityToken,
        region: stsToken.region,
        bucket: stsToken.bucket,
        endpoint: stsToken.endpoint
    });
}

/**
 * 根据检查部位获取OSS目标路径
 * @param checkPart 检查部位文件夹名称
 * @returns OSS存储路径
 */
function getOSSPath(checkPart: string): string {
    // 转换为小写并添加_imgs后缀
    return `${checkPart.toLowerCase()}_imgs`;
}

/**
 * 上传医学图像文件到OSS
 * @param files 图像文件列表(包含完整路径信息)
 * @param checkPart 检查部位
 * @returns 上传结果
 */
export async function uploadImageFiles(files: FileList, checkPart: string) {
    const client = await getOSSClient();
    const baseOssPath = getOSSPath(checkPart);
    const results = [];

    for (let i = 0; i < files.length; i++) {
        const file = files[i];

        try {
            // 获取文件的相对路径信息
            const fullPath = file.webkitRelativePath || file.name;
            let relativePath = fullPath;

            // 如果有相对路径信息，使用相对路径
            if (fullPath.includes('/')) {
                // 提取PID和子文件夹路径
                const pathParts = fullPath.split('/');

                // 至少有3层：部位/PID/检查类型
                if (pathParts.length >= 3) {
                    // 忽略第一层(检查部位)，从PID开始
                    relativePath = pathParts.slice(1).join('/');
                }
            }

            // 构建OSS路径
            const ossKey = `${baseOssPath}/${relativePath}`;

            // 上传文件
            const result = await client.put(ossKey, file);

            results.push({
                name: file.name,
                path: ossKey,
                url: result.url,
                success: true
            });
        } catch (err: any) {
            console.error('文件上传失败:', err);
            results.push({
                name: file.name,
                error: err.message || '未知错误',
                success: false
            });
        }
    }

    return {
        success: results.some(r => r.success), // 只要有一个成功就返回成功
        results: results
    };
}

/**
 * 上传心脏报告CSV文件到OSS
 * @param file CSV文件
 * @returns 上传结果
 */
export async function uploadHeartReportFile(file: File) {
    try {
        const client = await getOSSClient();
        const fileName = file.name;
        const ossKey = `heart_report/${fileName}`;

        // 上传文件到OSS
        const result = await client.put(ossKey, file);

        return {
            success: true,
            results: [{
                name: fileName,
                path: ossKey,
                url: result.url,
                success: true
            }]
        };
    } catch (err: any) {
        console.error('心脏报告上传失败:', err);
        return {
            success: false,
            results: [{
                name: file.name,
                error: err.message || '未知错误',
                success: false
            }]
        };
    }
}
