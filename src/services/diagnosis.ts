import api from './api'

export interface Subject {
    subject_id: string;
    check_date: string;
    name: string;
    pinyin: string;
    age: number;
    gender: string;
    remark: string;
    type: string;
    region: string;
    diagnosis_status: string;
}

export interface DiagnosisResult {
    subject_id: string
    name?: string
    pinyin: string
    age: number
    gender: string
    check_date: string
    region: string
    submit_time: string | null
    submit_user: string | null
    result: string
    status: string
    remark: string | null
}

export interface ImageSequence {
    name: string;
    region: string;
    images: string[];
    video?: string;
}

export interface SubjectsFilter {
    start_date?: string
    end_date?: string
    region?: string
}


/**
 * Fetches subjects based on filter criteria
 * Region is sent as query parameter, date range in request body
 */
export async function fetchSubjects(filter: SubjectsFilter): Promise<Subject[]> {
    const { region, ...dateFilter } = filter;
    return await api.post('/subjects', dateFilter, {
        params: region ? { region } : {}
    });
}

/**
 * Fetches a single subject by ID
 */
export async function fetchSubjectById(subjectId: string): Promise<Subject> {
    return await api.get(`/subjects/${subjectId}`)
}

/**
 * Fetches diagnosis result for a specific subject
 */
export async function fetchDiagnosisResult(subjectId: string, region?: string): Promise<DiagnosisResult> {
    const params = region ? { region } : {}
    return await api.get(`/subjects/${subjectId}/diagnosis`, { params })
}


/**
 * Saves diagnostic results specifically from the Diagnosis view
 * @param diagnosisData Object containing diagnosis data to save
 * @returns The saved diagnosis result
 */
export async function saveDiagnosticResult(diagnosisData: {
    subject_id: string;
    username: string;
    result: string;
    check_date: string;
    region: string;
    status: string;
}): Promise<DiagnosisResult> {
    // Validate required fields
    if (!diagnosisData.subject_id) {
        throw new Error('患者ID不能为空');
    }

    if (!diagnosisData.username) {
        throw new Error('用户名不能为空');
    }

    if (!diagnosisData.result || diagnosisData.result.trim() === '') {
        throw new Error('诊断结果不能为空');
    }

    // 调用诊断保存API，status可以是draft(暂存)或submitted(提交)
    return await api.post('/diagnosis/save', diagnosisData);
}

/**
 * Fetches image sequences for a specific subject and part
 */
export async function fetchImageSequences(subjectId: string, region: string): Promise<ImageSequence[]> {
    try {
        const response = await api.get(`/subjects/${subjectId}/sequences/imgs`, { params: { region } });

        // Convert dictionary format to array of sequences
        return Object.entries(response).map(([name, images]) => ({
            name,
            region,
            images: images as string[]
        }));
    } catch (error) {
        console.error('Error fetching image sequences:', error);
        return []; // Return empty array in case of error
    }
}



/**
 * 批量获取多个受试者的序列数量
 * @param subjectIds 受试者ID数组
 * @param region 区域名称（如head, abdomen等）
 * @returns 包含序列数量的数据对象
 */
export async function fetchSequencesCounts(
    subjectIds: string[],
    region: string
): Promise<{ data: Record<string, number> }> {
    try {
        return await api.post('/subjects/sequences/counts', subjectIds, {
            params: { region }
        });
    } catch (error) {
        console.error('Error fetching sequence counts:', error);
        // 返回所有ID对应的0计数
        const emptyResult: Record<string, number> = {};
        subjectIds.forEach(id => emptyResult[id] = 0);
        return { data: emptyResult };
    }
}

/**
 * 批量获取多个受试者的诊断老师信息
 * @param subjectIds 受试者ID数组
 * @param region 区域名称（如head, abdomen等）
 * @returns 包含诊断老师信息的数据对象
 */
export async function fetchSubmitUsers(
    subjectIds: string[],
    region: string
): Promise<{ data: Record<string, string> }> {
    try {
        return await api.post('/submit_users', subjectIds, {
            params: { region }
        });
    } catch (error) {
        console.error('Error fetching submit users:', error);
        // 返回所有ID对应的空诊断老师信息
        const emptyResult: Record<string, string> = {};
        subjectIds.forEach(id => emptyResult[id] = '');
        return { data: emptyResult };
    }
} 